﻿using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
public class GameManager : MonoBehaviour
{

    //Resources
    public List<Sprite> playerSprites;
    public Sprite weaponSprite;
    // public List<int> xpTable = new List<int>() { 8,17,25,35,45,55,65};
    [HideInInspector]
    public List<int> xpTable = new List<int>() { 8, 25, 40, 65, 90, 110, 135 };
    //only changing animators change the player skins
    public RuntimeAnimatorController[] playerSkins = new RuntimeAnimatorController[3];
    //References
    public Player player;
    public RectTransform healthBar;
    public Animator deathMenuAnimator;
    public Animator pauseMenuAnimator;
    public Animator toastMessageAnimator;
    public Text textToastMessage;
    public Text enemyKillText;
    [HideInInspector]
    public int gold;
    [HideInInspector]
    public int experience;
    [HideInInspector]
    public Weapon weapon;
    public GameData data;
    public List<Weapon> collectedWeapons;

    public bool isPaused;

    public static GameManager instance;
    public int playerCurrentLevel;
    public EnemyActivator enemyActivator;
    public ScenePortal scenePortal;
    public EnemyBatchHandler enemyBatchHandler;
    public Image switchWepImage;
    public bool isLevelCompleted; //to avoid Bug
    public Joystick movementJoystick;
    public int totalEnemies = 19;
    public SceneLoadingBarController loadLevel;
    private Transform weaponContainer;
    private int currentenemyKill = -1;

    private void Awake()
    {
        if (instance == null)
            instance = this;
    }

    private void Start() {
        // Ensure DataController is properly initialized
        if (DataController.instance == null) {
            Debug.LogError("DataController.instance is null! Make sure DataController is in the scene.");
            return;
        }

        // Ensure player is assigned
        if (player == null) {
            Debug.LogError("Player is not assigned in GameManager!");
            return;
        }

        // Ensure player has weaponContainer
        if (player.weaponContainer == null) {
            Debug.LogError("Player weaponContainer is null!");
            return;
        }

        weaponContainer = player.weaponContainer.transform;
        data = DataController.instance.data;

        // Ensure data is not null
        if (data == null) {
            Debug.LogError("GameData is null! Creating new data.");
            data = new GameData();
            DataController.instance.data = data;
        }

        LoadData();
        UpdateEnemyKillText();
    }

    //floatingText;
    public void ShowText(string msg, int fontSize, Color color, Vector3 position, Vector3 motion, float duration)
    {
        if (!player.isOnPc)
            fontSize += 7;
        FloatingTextManager.instance.Show(msg, fontSize, color, position, motion, duration);

    }

    public void ShowToastMessage(string message, float duration) {
        textToastMessage.text = message;
        toastMessageAnimator.SetBool("ShowPanel", true);
        Invoke("HideToastMessage", duration);
    }

    private void HideToastMessage() {
        toastMessageAnimator.SetBool("ShowPanel", false);
    }

    //Unlock Characters
    public bool TryUnlockSkin(int skinNumber, int skinPrice) {
        if (data.skins[skinNumber])
            return true;
        //do we have enough gold? if so upgrade and decrement the weaponprice from the gold
        if (gold >= skinPrice) {
            gold -= skinPrice;
            data.skins[skinNumber] = true;
            data.selectedSkin = skinNumber;
            player.ChangeSkin(skinNumber);
            return true;
        }
        return false;
    }

    //Weapon System
    public bool TryUpgradeWeapon()
    {
        //is the weapon max level
        if (weapon.weaponPrices.Count == weapon.weaponLevel)
            return false;
        //do we have enough gold? if so upgrade and decrement the weaponprice from the gold
        else if(gold >= weapon.weaponPrices[weapon.weaponLevel]){
            gold -= weapon.weaponPrices[weapon.weaponLevel];
            weapon.UpgradeWeapon();
            SaveData();
            return true;

        }
        return false;
    }
    //Weapon System
    public bool TryUpgradeWeapon(Weapon wep) {
        //is the weapon max level
        if (wep.weaponPrices.Count == wep.weaponLevel)
            return false;
        //do we have enough gold? if so upgrade and decrement the weaponprice from the gold
        else if (gold >= wep.weaponPrices[wep.weaponLevel]) {
            gold -= wep.weaponPrices[wep.weaponLevel];
            wep.UpgradeWeapon();
            SaveData();
            return true;

        }
        return false;
    }

    public bool UnlockWeapon() {
        if (data.notCollectedWeapons.Count == 0)
            return false;
        int dataID;
        dataID = data.notCollectedWeapons[Random.Range(0, data.notCollectedWeapons.Count)];
        data.collectedWeapons.Add(dataID);
        Weapon playerWep = CheckWeapons(dataID, collectedWeapons.Count);
        data.weaponSelected = collectedWeapons.Count;
        collectedWeapons.Add(playerWep);
        playerWep.enabled = true;
        playerWep.ChangeSprites();
        data.notCollectedWeapons.Remove(dataID);
        ChangeSwitchWeaponButtonImage();
        SaveData();
        return true;
    }

    public void SwitchWeapon() {

        if (weapon.weaponID == collectedWeapons.Count - 1) {
            data.weaponSelected = 0;
            Weapon wep = collectedWeapons[0];
            wep.enabled = true;
            weaponSprite = wep.GunSide;
            wep.ChangeSprites();
        }
        else {
            data.weaponSelected = weapon.weaponID + 1;
            Weapon wep = collectedWeapons[weapon.weaponID + 1] as Weapon;
            wep.enabled = true;
            weaponSprite = wep.GunSide;
            wep.ChangeSprites();

        }
    }

    public void ChangeSwitchWeaponButtonImage() {
        switchWepImage.sprite = weaponSprite;
    }

    //if weapon is lastselected weapon before closing game enable else disable
    private bool IsSelectedWeapon(Weapon weapon) {
        if (weapon.weaponID == data.weaponSelected) {
            weapon.ChangeSprites();
            switchWepImage.sprite = weapon.GunSide;
            // Set this weapon as the current weapon in GameManager
            this.weapon = weapon;
            return true;
        }
        return false;
    }
    //checking collected weapons, if weapon is collected then give it a ID
    public Weapon CheckWeapons(int dataWepID, int newID) {
        Weapon temp = weaponContainer.GetChild(dataWepID).GetComponent<Weapon>();
        temp.weaponID = newID;
        temp.weaponLevel = data.weaponLevel[newID];
        temp.enabled = IsSelectedWeapon(temp);
        return temp;
    }

    public int GetCurrentWeaponDamage() {
        if(weapon.weaponLevel == weapon.weaponPrices.Count)
            return weapon.damagePoint[weapon.weaponLevel -1];
        return weapon.damagePoint[weapon.weaponLevel];
    }

    public float GetCurrentWeaponPushForce() {
        if (weapon.weaponLevel == weapon.weaponPrices.Count)
            return weapon.pushForce[weapon.weaponLevel-1];
        return weapon.pushForce[weapon.weaponLevel];
    }

    //experience system
    public int GetCurrentLevel() {
        int r = 0;
        int add = 0;
        while (experience >= add) {
            add += xpTable[r];
            r++;
            if (r == xpTable.Count)// max level
                return r;
        }
        return r;
    }
    public int GetXpFromLevel(int level) {
        int r = 0;
        int xp = 0;

        while (r < level) {
            xp += xpTable[r];
            r++;
        }

        return xp;
    }
    public void GrantXp(int xp) {
        int currentLevel = GetCurrentLevel();
        experience += xp;
        if (currentLevel < GetCurrentLevel())
            OnLevelUp();
    }

    public void OnLevelUp() {
        ShowText("Level Up!", 25, Color.green, player.transform.position, Vector3.up * 50, 1.0f);
        playerCurrentLevel += 1;
        player.SetLevelHealth(playerCurrentLevel);
        OnHealthChange();
    }

    //player health bar

    public void OnHealthChange() {
        float ratio = (float)player.health / player.maxHealth;
        healthBar.localScale = new Vector2(ratio, 1);
    }

    public void ShowPauseMenu() {
        pauseMenuAnimator.SetTrigger("Show");
        isPaused = true;
    }

    public void HidePauseMenu() {
        pauseMenuAnimator.SetTrigger("Hide");
        isPaused = false;
    }

    public void LevelComplete() {
        if (!isLevelCompleted) {
            isLevelCompleted = true;
            AudioController.instance.PlaySound(SoundClip.victory);
            //ShowText("Level Complete", 23, Color.blue, player.transform.position + (Vector3.up * 0.16f), Vector3.zero, 5f);
            ShowToastMessage("Level Complete!", 5f);
        }

    }

    // Save  Data
    public void SaveData() {
        if (data == null) {
            Debug.LogError("Cannot save data - GameData is null!");
            return;
        }

        data.gold = gold;
        data.experience = experience;

        // Only save weapon data if weapon is not null
        if (weapon != null) {
            data.weaponSelected = weapon.weaponID;
            data.weaponLevel[weapon.weaponID] = weapon.weaponLevel;
        }
        else {
            Debug.LogWarning("Weapon is null when saving data - weapon data not saved");
        }

        // currentDungeonLevel is already updated in AdvanceToNextDungeon() method
        if (DataController.instance != null) {
            DataController.instance.SaveData(data);
        }
        else {
            Debug.LogError("DataController.instance is null when trying to save data!");
        }
    }

    // Load data
    public void LoadData() {
        player.ChangeSkin(data.selectedSkin);
        gold = data.gold;
        experience = data.experience;

        // Ensure currentDungeonLevel is valid (1-4)
        if (data.currentDungeonLevel < 1 || data.currentDungeonLevel > 4) {
            data.currentDungeonLevel = 1;
        }

        int i = 0;
        //checking collected weapons, i is the sequential id of collected weapons
        //dataWepID is the child number of the wepcontainer that has weapon script
        foreach (int dataWepID in data.collectedWeapons) {
            Weapon wep = CheckWeapons(dataWepID, i);
            collectedWeapons.Add(wep);
            wep.enabled = IsSelectedWeapon(wep);
            i++;

        }

        // Ensure we have a weapon selected - if not, default to the first weapon
        if (weapon == null && collectedWeapons.Count > 0) {
            weapon = collectedWeapons[0];
            weapon.enabled = true;
            weapon.ChangeSprites();
            if (switchWepImage != null) {
                switchWepImage.sprite = weapon.GunSide;
            }
            Debug.Log("No weapon was selected, defaulting to first weapon: " + weapon.name);
        }

        playerCurrentLevel = GetCurrentLevel() - 1;
        if (playerCurrentLevel > 1) {
            //player health based on experience, -1 because level 1 counts as well to increase health
            player.SetLevelHealth(playerCurrentLevel);
            //OnHealthChange();
        }
        ActivateEnemy();
    }

    public void UpdateEnemyKillText() {
        currentenemyKill += 1;
        if (currentenemyKill < 10)
            enemyKillText.text = "0" + currentenemyKill + "/" + totalEnemies;
        else enemyKillText.text = "" + currentenemyKill + "/" + totalEnemies;
    }

    public void ClearData() {
        SaveData();
    }

  /*  public void OnDestroy() {

        SaveData();
        PlayerPrefs.SetString("MySettingsEditor", "");
        PlayerPrefs.SetString("MySettings", "");
    }*/

   //enemy activation and opening doors if player completes the current level

    public void ActivateEnemy() {
        enemyActivator.ActivateFirstEnemyBatch();
    }

    public void OpenBarrier(int lockerID) {
        if(enemyActivator != null) {
            enemyActivator.OpenBarrier(lockerID);
        }

    }

    public void OpenDoor() {
        scenePortal.OpenDoor();
    }

    public void LoadLevel(string name) {
        SaveData();
        loadLevel.gameObject.SetActive(true);
        loadLevel.LoadLevel(name);
    }

    // New method to advance to the next dungeon in sequence
    public void AdvanceToNextDungeon() {
        data.currentDungeonLevel++;
        if (data.currentDungeonLevel > 5) {
            // After completing the final dungeon, show victory and restart
            ShowVictoryAndRestart();
            return;
        }
        SaveData();
        string nextDungeonName = "Dungeon_" + data.currentDungeonLevel;
        LoadLevel(nextDungeonName);
    }

    // Method to get the current dungeon scene name
    public string GetCurrentDungeonName() {
        return "Dungeon_" + data.currentDungeonLevel;
    }

    // Method to get the next dungeon scene name
    public string GetNextDungeonName() {
        int nextLevel = data.currentDungeonLevel + 1;
        if (nextLevel > 5) {
            return "Victory"; // After final dungeon, victory
        }
        return "Dungeon_" + nextLevel;
    }

    // Method to get the current dungeon level (1-5)
    public int GetCurrentDungeonLevel() {
        if (data == null) {
            return 1; // Default to dungeon 1 if no data
        }
        return Mathf.Clamp(data.currentDungeonLevel, 1, 5);
    }

    // Method to check if current dungeon is the final boss dungeon
    public bool IsFinalDungeon() {
        return data != null && data.currentDungeonLevel == 5;
    }

    // Method to handle victory and restart the game
    public void ShowVictoryAndRestart() {
        // Show victory message
        ShowText("VICTORY! You have defeated the final boss!", 40, Color.gold, Vector3.zero, Vector3.zero, 3.0f);

        // Reset to first dungeon after a delay
        Invoke("RestartGame", 3.0f);
    }

    // Method to restart the game from the beginning
    public void RestartGame() {
        data.currentDungeonLevel = 1;
        SaveData();
        LoadLevel("Dungeon_1");
    }

    /*/// <summary>
/// int preferedSkin,
/// int pesosAmount,
/// int experience
/// int weaponLevel
/// </summary>
//save state
public void SaveState() {
    string s = "";
    s += "0" + "|";
    s += gold.ToString() + "|";
    s += experience.ToString();
    s += weapon.weaponLevel.ToString();

    PlayerPrefs.SetString("Setting", s);

    //PlayerPrefs.DeleteAll();
}


public void LoadState(Scene scene, LoadSceneMode mode) {
    if (!PlayerPrefs.HasKey("Setting"))
        return;
    string[] data = PlayerPrefs.GetString("Setting").Split('|');

    //Change PlayerScreen
    // amount of pessos
    gold = int.Parse(data[1]);
    experience = int.Parse(data[2]);
    //change weapon level
    weapon.weaponLevel = int.Parse(data[3]);
}*/

}
