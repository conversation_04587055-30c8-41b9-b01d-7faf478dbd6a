using UnityEngine;

/// <summary>
/// Final Boss enemy that triggers victory condition when defeated
/// </summary>
public class FinalBoss : Enemy {

    [Header("Final Boss Settings")]
    public bool isFinalBoss = true;
    public string victoryMessage = "You have defeated the ultimate evil!";
    
    protected override void Start() {
        base.Start();
        
        // Make the final boss more challenging
        if (isFinalBoss) {
            // Increase health for final boss
            maxHealth = Mathf.Max(maxHealth, 100);
            health = maxHealth;
            
            // Increase rewards
            xpValue = Mathf.Max(xpValue, 50);
            goldValue = Mathf.Max(goldValue, 100);
            
            // Show dramatic entrance message
            if (GameManager.instance != null) {
                GameManager.instance.ShowText("THE FINAL BOSS AWAKENS!", 50, Color.red, 
                    transform.position, Vector3.up * 60, 2.0f);
            }
        }
    }

    protected override void Death() {
        isDeath = true;
        anim.SetTrigger("EnemyDeath");
        
        // Play special final boss death sound
        AudioController.instance.PlaySound(SoundClip.enemyDeath);
        
        // Remove from enemy batch handler
        if (transform.parent != null) {
            EnemyBatchHandler batchHandler = transform.parent.GetComponent<EnemyBatchHandler>();
            if (batchHandler != null) {
                batchHandler.RemoveEnemy(transform);
            }
        }
        
        // Grant massive XP and gold rewards
        GameManager.instance.GrantXp(xpValue);
        GameManager.instance.ShowText("+" + xpValue + " XP!", 40, Color.magenta, 
            transform.position, Vector3.up * 80, 2.0f);
        
        GameManager.instance.gold += goldValue;
        GameManager.instance.ShowText("+" + goldValue + " GOLD!", 35, Color.yellow, 
            transform.position, Vector3.up * 40, 2.0f);
        
        // Show victory message
        GameManager.instance.ShowText(victoryMessage, 45, Color.gold, 
            transform.position, Vector3.up * 120, 3.0f);
        
        // Trigger victory condition after a delay to let animations play
        if (isFinalBoss && GameManager.instance.IsFinalDungeon()) {
            Invoke("TriggerVictory", 2.0f);
        }
        
        // Destroy the boss
        Destroy(gameObject, 3.0f);
    }
    
    private void TriggerVictory() {
        if (GameManager.instance != null) {
            GameManager.instance.ShowVictoryAndRestart();
        }
    }
    
    // Override to make final boss more aggressive
    protected override void ReceiveDamage(Damage dmg) {
        if (isDeath || startAnim)
            return;
            
        base.ReceiveDamage(dmg);
        UpdateHealthBar();
        Hurt();
        
        // Show dramatic damage text for final boss
        if (isFinalBoss) {
            GameManager.instance.ShowText("-" + dmg.damageValue + "!", 25, Color.white, 
                transform.position, Vector3.up * 20, 1.0f);
        }
    }
}
