Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.5f1 (923722cbbcfc) revision 9582370'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 15897 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-01T12:36:37Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.5f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/Downloads/Engkwentro-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Engkwentro-The_Filipino_Myths_Chronicle
-logFile
Logs/AssetImportWorker2.log
-srvPort
50488
-job-worker-count
6
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Downloads/Engkwentro-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Engkwentro-The_Filipino_Myths_Chronicle
C:/Users/<USER>/Downloads/Engkwentro-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Engkwentro-The_Filipino_Myths_Chronicle
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20856]  Target information:

Player connection [20856]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 1947270203 [EditorId] 1947270203 [Version] 1048832 [Id] WindowsEditor(7,SSI-JSantonia-EBG) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20856] Host joined multi-casting on [***********:54997]...
Player connection [20856] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 6
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 34.74 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.5f1 (923722cbbcfc)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Downloads/Engkwentro-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Engkwentro-The_Filipino_Myths_Chronicle/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) Graphics (ID=0x7d45)
    Vendor:   Intel
    VRAM:     9061 MB
    Driver:   32.0.101.6556
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56684
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.010271 seconds.
- Loaded All Assemblies, in  1.272 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.100 seconds
Domain Reload Profiling: 2365ms
	BeginReloadAssembly (436ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (94ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (126ms)
	LoadAllAssembliesAndSetupDomain (574ms)
		LoadAssemblies (424ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (564ms)
			TypeCache.Refresh (557ms)
				TypeCache.ScanAssembly (519ms)
			BuildScriptInfoCaches (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1102ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (963ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (164ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (189ms)
			ProcessInitializeOnLoadAttributes (423ms)
			ProcessInitializeOnLoadMethodAttributes (177ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
DirectoryNotFoundException: Could not find a part of the path "C:\Users\<USER>\Downloads\Engkwentro-The_Filipino_Myths_Chronicle (2)\Engkwentro-The_Filipino_Myths_Chronicle\Engkwentro-The_Filipino_Myths_Chronicle\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll"
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)
  at Mono.Cecil.ModuleDefinition.GetFileStream (System.String fileName, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share) [0x00006] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at Mono.Cecil.ModuleDefinition.ReadModule (System.String fileName, Mono.Cecil.ReaderParameters parameters) [0x00008] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at Mono.Cecil.AssemblyDefinition.ReadAssembly (System.String fileName, Mono.Cecil.ReaderParameters parameters) [0x00000] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at UnityEditor.AssemblyValidation.LoadAssemblyDefinitions (System.String[] assemblyPaths, System.String[] searchPaths) [0x00048] in <1d4abd3cd5ee415ab29235fc867cd8e6>:0 
  at UnityEditor.AssemblyValidation.ValidateAssemblies (System.String[] assemblyPaths, System.Boolean enableLogging) [0x00007] in <1d4abd3cd5ee415ab29235fc867cd8e6>:0 

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.323 seconds
Refreshing native plugins compatible for Editor in 20.81 ms, found 2 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 19.51 ms, found 2 plugins.
System.IO.DirectoryNotFoundException: Could not find a part of the path "C:\Users\<USER>\Downloads\Engkwentro-The_Filipino_Myths_Chronicle (2)\Engkwentro-The_Filipino_Myths_Chronicle\Engkwentro-The_Filipino_Myths_Chronicle\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\localization\plastic-gui.en.txt"
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean isAsync, System.Boolean anonymous) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess)
  at Codice.Utils.LocalizationTranslator`1+FromTxt[T].GetTranslationsFromFile (System.String file, System.Boolean bLogExceptions) [0x0001b] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Codice.Utils.LocalizationTranslator`1[T].BuildFromLocalizationFileOnDir (System.String localizationDirPath, System.String fileName, System.String culture) [0x00089] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Codice.Utils.LocalizationTranslator`1[T].BuildFromLocalizationFile (System.String fileName, System.String culture) [0x00006] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at PlasticGui.PlasticLocalization.GetTranslator (System.String fileName, System.String culture) [0x00011] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at PlasticGui.PlasticLocalization.GetString (PlasticGui.PlasticLocalization+Name name) [0x00001] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Unity.Cloud.Collaborate.ToolbarButton..ctor () [0x00017] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:30 
  at Unity.Cloud.Collaborate.ToolbarButton.InitializeIfNeeded () [0x00000] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:22 
  at Unity.Cloud.Collaborate.ToolbarBootstrap..cctor () [0x00000] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:14 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.491 seconds
Domain Reload Profiling: 2805ms
	BeginReloadAssembly (492ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (77ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (87ms)
	LoadAllAssembliesAndSetupDomain (631ms)
		LoadAssemblies (562ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (328ms)
			TypeCache.Refresh (224ms)
				TypeCache.ScanAssembly (183ms)
			BuildScriptInfoCaches (90ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1492ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1215ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (252ms)
			ProcessInitializeOnLoadAttributes (517ms)
			ProcessInitializeOnLoadMethodAttributes (408ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 15.26 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 2838 unused Assets / (0.8 MB). Loaded Objects now: 3301.
Memory consumption went from 81.1 MB to 80.4 MB.
Total: 24.644200 ms (FindLiveObjects: 1.090800 ms CreateObjectMapping: 1.181300 ms MarkObjects: 20.807600 ms  DeleteObjects: 1.561000 ms)

========================================================================
Received Import Request.
  Time since last request: 230192.849143 seconds.
  path: Assets/Artwork/Animations/Player/player_0_hit.anim
  artifactKey: Guid(d76ad3c341394ca4991f9605fd5cff5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Animations/Player/player_0_hit.anim using Guid(d76ad3c341394ca4991f9605fd5cff5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9d838efaa14a635e7eaae6f80edbb33') in 0.0859059 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
DirectoryNotFoundException: Could not find a part of the path "C:\Users\<USER>\Downloads\Engkwentro-The_Filipino_Myths_Chronicle (2)\Engkwentro-The_Filipino_Myths_Chronicle\Engkwentro-The_Filipino_Myths_Chronicle\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll"
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)
  at Mono.Cecil.ModuleDefinition.GetFileStream (System.String fileName, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share) [0x00006] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at Mono.Cecil.ModuleDefinition.ReadModule (System.String fileName, Mono.Cecil.ReaderParameters parameters) [0x00008] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at Mono.Cecil.AssemblyDefinition.ReadAssembly (System.String fileName, Mono.Cecil.ReaderParameters parameters) [0x00000] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at UnityEditor.AssemblyValidation.LoadAssemblyDefinitions (System.String[] assemblyPaths, System.String[] searchPaths) [0x00048] in <1d4abd3cd5ee415ab29235fc867cd8e6>:0 
  at UnityEditor.AssemblyValidation.ValidateAssemblies (System.String[] assemblyPaths, System.Boolean enableLogging) [0x00007] in <1d4abd3cd5ee415ab29235fc867cd8e6>:0 

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.222 seconds
Refreshing native plugins compatible for Editor in 19.76 ms, found 2 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
System.IO.DirectoryNotFoundException: Could not find a part of the path "C:\Users\<USER>\Downloads\Engkwentro-The_Filipino_Myths_Chronicle (2)\Engkwentro-The_Filipino_Myths_Chronicle\Engkwentro-The_Filipino_Myths_Chronicle\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\localization\plastic-gui.en.txt"
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean isAsync, System.Boolean anonymous) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess)
  at Codice.Utils.LocalizationTranslator`1+FromTxt[T].GetTranslationsFromFile (System.String file, System.Boolean bLogExceptions) [0x0001b] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Codice.Utils.LocalizationTranslator`1[T].BuildFromLocalizationFileOnDir (System.String localizationDirPath, System.String fileName, System.String culture) [0x00089] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Codice.Utils.LocalizationTranslator`1[T].BuildFromLocalizationFile (System.String fileName, System.String culture) [0x00006] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at PlasticGui.PlasticLocalization.GetTranslator (System.String fileName, System.String culture) [0x00011] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at PlasticGui.PlasticLocalization.GetString (PlasticGui.PlasticLocalization+Name name) [0x00001] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Unity.Cloud.Collaborate.ToolbarButton..ctor () [0x00017] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:30 
  at Unity.Cloud.Collaborate.ToolbarButton.InitializeIfNeeded () [0x00000] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:22 
  at Unity.Cloud.Collaborate.ToolbarBootstrap..cctor () [0x00000] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:14 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.737 seconds
Domain Reload Profiling: 2953ms
	BeginReloadAssembly (469ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (89ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (559ms)
		LoadAssemblies (578ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (218ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (193ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (1739ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1417ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (285ms)
			ProcessInitializeOnLoadAttributes (529ms)
			ProcessInitializeOnLoadMethodAttributes (559ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 48.35 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 2836 unused Assets / (0.7 MB). Loaded Objects now: 3304.
Memory consumption went from 80.0 MB to 79.4 MB.
Total: 28.180900 ms (FindLiveObjects: 1.372500 ms CreateObjectMapping: 0.883800 ms MarkObjects: 23.901400 ms  DeleteObjects: 1.861300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 452.779531 seconds.
  path: Assets/Prefabs/AudioManager.prefab
  artifactKey: Guid(546b80b5979035d4983ddc3409b0e96b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/AudioManager.prefab using Guid(546b80b5979035d4983ddc3409b0e96b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '017b9c96488befa649957054931c5d73') in 8.3858601 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/Virtual Joystick Pack/Prefabs/Variable Joystick.prefab
  artifactKey: Guid(b737c9d1a3e7e174793d05f4637d64e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Virtual Joystick Pack/Prefabs/Variable Joystick.prefab using Guid(b737c9d1a3e7e174793d05f4637d64e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b071a73f59cffe0d390e94d1fa9f0d4d') in 0.01573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000132 seconds.
  path: Assets/Artwork/Level/Tiles/Lava/Underworld.prefab
  artifactKey: Guid(cad7a358c00724943bc85632ac243ea6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Level/Tiles/Lava/Underworld.prefab using Guid(cad7a358c00724943bc85632ac243ea6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '55bcfbdcbc5b1f0425fffb2eedf7ebbb') in 0.430662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 213

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_TinyZombie.prefab
  artifactKey: Guid(adea71639d41aa04dad97561605982d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_TinyZombie.prefab using Guid(adea71639d41aa04dad97561605982d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 18.24 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'd59b9c2dfd6689bcf584b2a6247a8efc') in 0.1118604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000112 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_IceZombie.prefab
  artifactKey: Guid(8c6d39defe1324645b90471931b8a791) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_IceZombie.prefab using Guid(8c6d39defe1324645b90471931b8a791) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 23.22 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'e97451ee4778e8299c8768f8983c171f') in 0.0996313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Prefabs/Weapons/Laser.prefab
  artifactKey: Guid(b994ba5868c9f134c818e5effadbca7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/Laser.prefab using Guid(b994ba5868c9f134c818e5effadbca7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a84e16bc515a8c1124b3963266ffbc0') in 0.017845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_OrcWarrior.prefab
  artifactKey: Guid(e3ce053cb58827c4e84976bc82cc9b12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_OrcWarrior.prefab using Guid(e3ce053cb58827c4e84976bc82cc9b12) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 24.82 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'f38e5f8ec844d58e26b1a53378467cc1') in 0.0989442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Resources/white_bullet.prefab
  artifactKey: Guid(83b1d6cc27b1b6f489d573c3572f9c7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/white_bullet.prefab using Guid(83b1d6cc27b1b6f489d573c3572f9c7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7004c22a5d11e7b82006eb676d5438e') in 0.0349733 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Prefabs/Others/NPC/Canvas_NPC.prefab
  artifactKey: Guid(060e6e57d1ff793449ca4e2b1b487f53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/NPC/Canvas_NPC.prefab using Guid(060e6e57d1ff793449ca4e2b1b487f53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83cbb8f7e696d9449be07929713b069c') in 0.0302727 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Prefabs/Enemies/Boses/Boss_Big_Demon.prefab
  artifactKey: Guid(fa4cbbc38dc82f24096aeba6ccc0e508) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Boses/Boss_Big_Demon.prefab using Guid(fa4cbbc38dc82f24096aeba6ccc0e508) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 36.68 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'fb61b4986ffecc0f0e091261e4b26ca8') in 0.1485369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 239.331650 seconds.
  path: Assets/Prefabs/UI/Canvas_Hud.prefab
  artifactKey: Guid(08573078048e167478107ea35eea1b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/UI/Canvas_Hud.prefab using Guid(08573078048e167478107ea35eea1b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f395de2d8f2c6dfb9e167bf906d832e8') in 0.0691841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 347

