fileFormatVersion: 2
guid: 8501e10b1eafde34586080ba342fdc0e
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: TX Shadow Chest
  - first:
      213: 21300002
    second: TX Shadow Cube
  - first:
      213: 21300004
    second: TX Shadow Crate
  - first:
      213: 21300006
    second: TX Shadow Crate Small
  - first:
      213: 21300008
    second: TX Shadow Chest Opened
  - first:
      213: 21300010
    second: TX Shadow Rune Pillar X2
  - first:
      213: 21300012
    second: TX Shadow Rune Pillar X3
  - first:
      213: 21300014
    second: TX Shadow Rune Pillar Broken
  - first:
      213: 21300016
    second: TX Shadow Tileset Wall 3
  - first:
      213: 21300018
    second: TX Shadow Tileset Wall 2
  - first:
      213: 21300020
    second: TX Shadow Tileset Wall 1
  - first:
      213: 21300022
    second: TX Shadow Barrel
  - first:
      213: 21300024
    second: TX Shadow Gravestone A
  - first:
      213: 21300026
    second: TX Shadow Gravestone C
  - first:
      213: 21300028
    second: TX Shadow Gravestone B
  - first:
      213: 21300030
    second: TX Shadow Tree T3
  - first:
      213: 21300032
    second: TX Shadow Tileset Wall 5
  - first:
      213: 21300034
    second: TX Shadow Tileset Wall 4
  - first:
      213: 21300036
    second: <PERSON> Shadow Stone Coffin Vert
  - first:
      213: 21300038
    second: <PERSON> <PERSON> Stone Coffin Hori
  - first:
      213: 21300040
    second: TX Shadow Bush T6
  - first:
      213: 21300042
    second: TX Shadow Road Sign E
  - first:
      213: 21300044
    second: TX Shadow Road Sign W
  - first:
      213: 21300046
    second: TX Shadow Pot A
  - first:
      213: 21300048
    second: TX Shadow Pot B
  - first:
      213: 21300050
    second: TX Shadow Pot C
  - first:
      213: 21300052
    second: TX Shadow Stone Bench S
  - first:
      213: 21300054
    second: TX Shadow Stone Bench W
  - first:
      213: 21300056
    second: TX Shadow Stone Bench E
  - first:
      213: 21300058
    second: TX Shadow Stone T1
  - first:
      213: 21300060
    second: TX Shadow Stone T2
  - first:
      213: 21300062
    second: TX Shadow Stone T3
  - first:
      213: 21300064
    second: TX Shadow Stone T4
  - first:
      213: 21300066
    second: TX Shadow Stone T5
  - first:
      213: 21300068
    second: TX Shadow Stone T6
  - first:
      213: 21300070
    second: TX Shadow Statue
  - first:
      213: 21300072
    second: TX Shadow Stone Lantern
  - first:
      213: 21300074
    second: TX Shadow Bridge Gate
  - first:
      213: 21300076
    second: TX Shadow Pillar
  - first:
      213: 21300078
    second: TX Shadow Pillar Broken
  - first:
      213: 21300080
    second: TX Shadow Tree T1
  - first:
      213: 21300082
    second: TX Shadow Tree T2
  - first:
      213: 21300084
    second: TX Shadow Stone Cube
  - first:
      213: 21300086
    second: TX Shadow Bush T1
  - first:
      213: 21300088
    second: TX Shadow Bush T5
  - first:
      213: 21300090
    second: TX Shadow Bush T2
  - first:
      213: 21300092
    second: TX Shadow Bush T4
  - first:
      213: 21300094
    second: TX Shadow Bush T3
  - first:
      213: 21300096
    second: TX Shadow Altar
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 4
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: TX Shadow Tree T3
      rect:
        serializedVersion: 2
        x: 48
        y: 360
        width: 86
        height: 52
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0fb913af1a3cab246b51f84e61e9a6b0
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TX Shadow Bush T6
      rect:
        serializedVersion: 2
        x: 348
        y: 285
        width: 41
        height: 29
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4bb2cc55da069424e8aff94284d57aed
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TX Shadow Tree T1
      rect:
        serializedVersion: 2
        x: 304
        y: 361
        width: 74
        height: 40
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f8168048d08df164a8bbb2e9aadc5db2
      internalID: 21300080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TX Shadow Tree T2
      rect:
        serializedVersion: 2
        x: 172
        y: 361
        width: 83
        height: 46
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 32229d5ef376e3342b25600dfcc4aadd
      internalID: 21300082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TX Shadow Bush T1
      rect:
        serializedVersion: 2
        x: 39
        y: 293
        width: 22
        height: 12
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4829c4186c54a4d4aa79288537bd6803
      internalID: 21300086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TX Shadow Bush T5
      rect:
        serializedVersion: 2
        x: 285
        y: 279
        width: 38
        height: 24
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 75d0ea0c6c657374bae51f6cf2af4057
      internalID: 21300088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TX Shadow Bush T2
      rect:
        serializedVersion: 2
        x: 99
        y: 290
        width: 28
        height: 17
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7417a256143eb114b9430a9897a56774
      internalID: 21300090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TX Shadow Bush T4
      rect:
        serializedVersion: 2
        x: 220
        y: 283
        width: 48
        height: 24
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ba47d8abd25e4a74d994a7b3c9a96e2e
      internalID: 21300092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: TX Shadow Bush T3
      rect:
        serializedVersion: 2
        x: 160
        y: 289
        width: 37
        height: 17
      alignment: 7
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3653b1c6dac6bef46bacba0646cb3204
      internalID: 21300094
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 901f89b6ee1d4c64cb2963d1fac91fa4
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      TX Shadow Tree T1: 21300080
      TX Shadow Bush T2: 21300090
      TX Shadow Tree T2: 21300082
      TX Shadow Bush T5: 21300088
      TX Shadow Bush T1: 21300086
      TX Shadow Tree T3: 21300030
      TX Shadow Bush T4: 21300092
      TX Shadow Bush T3: 21300094
      TX Shadow Bush T6: 21300040
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 187605
  packageName: Pixel Art Top Down - Basic
  packageVersion: 1.2.0
  assetPath: Assets/Cainos/Pixel Art Top Down - Basic/Texture/TX Shadow Plant.png
  uploadId: 759042
