{"Nodes": [{"Annotation": "all_tundra_nodes", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150], "DebugActionIndex": 0}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt", "DisplayName": "Writing Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt", "ActionType": "WriteFile", "PayloadOffset": 116, "PayloadLength": 147, "PayloadDebugContentSnippet": "C:\\Users\\<USER>\\Dow", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"], "OutputFlags": [2], "DebugActionIndex": 1}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp", "DisplayName": "Writing Unity.Multiplayer.Center.Common.rsp", "ActionType": "WriteFile", "PayloadOffset": 365, "PayloadLength": 35406, "PayloadDebugContentSnippet": "-target:library\n-out:\"Library/", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"], "OutputFlags": [2], "DebugActionIndex": 2}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2", "DisplayName": "Writing Unity.Multiplayer.Center.Common.rsp2", "ActionType": "WriteFile", "PayloadOffset": 35874, "PayloadLength": 160, "PayloadDebugContentSnippet": "/pathmap:\"C:\\Users\\<USER>", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"], "OutputFlags": [2], "DebugActionIndex": 3}, {"Annotation": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)", "DisplayName": "Compiling C# (Unity.Multiplayer.Center.Common)", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetCoreRuntime\\dotnet.exe\" exec \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp\" \"@Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEditor.Graphs.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/AnswerData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/IOnboardingSection.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/IOnboardingSectionAnalyticsProvider.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/Preset.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/SelectedSolutionsData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/StyleConstants.cs", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.pdb", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"], "OutputFlags": [0, 0, 0], "ToBuildDependencies": [1, 2, 3, 126], "ToUseDependencies": [1, 3], "AllowUnexpectedOutput": true, "Env": [{"Key": "DOTNET_MULTILEVEL_LOOKUP", "Value": "0"}], "CachingMode": "ByDirectInputs", "DebugActionIndex": 4}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_F0EC10369ED1F6F8.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.Graphs.dll_F0EC10369ED1F6F8.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEditor.Graphs.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_F0EC10369ED1F6F8.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 5}, {"Annotation": "ScriptAssemblies", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 131, 132, 133, 140, 143, 144, 145, 146, 147, 148], "DebugActionIndex": 6}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B5D72C560403CEFB.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.AccessibilityModule.dll_B5D72C560403CEFB.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.AccessibilityModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B5D72C560403CEFB.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 7}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.AdaptivePerformanceModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 8}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_6990DCC0F76DDF62.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.BuildProfileModule.dll_6990DCC0F76DDF62.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.BuildProfileModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_6990DCC0F76DDF62.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 9}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_F8CF1649485C58E1.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.CoreBusinessMetricsModule.dll_F8CF1649485C58E1.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreBusinessMetricsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_F8CF1649485C58E1.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 10}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_2E35969D6589B2C6.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.CoreModule.dll_2E35969D6589B2C6.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_2E35969D6589B2C6.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 11}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_6E00666893D93FDB.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.DeviceSimulatorModule.dll_6E00666893D93FDB.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_6E00666893D93FDB.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 12}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2CC00B8BFD198369.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.DiagnosticsModule.dll_2CC00B8BFD198369.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2CC00B8BFD198369.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 13}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_69BDDF7412CC3BEF.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.dll_69BDDF7412CC3BEF.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.dll_69BDDF7412CC3BEF.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 14}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 15}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD7A8275FFD2CFCF.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.EmbreeModule.dll_AD7A8275FFD2CFCF.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EmbreeModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD7A8275FFD2CFCF.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 16}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CC20AC623A7598A9.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GIModule.dll_CC20AC623A7598A9.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GIModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CC20AC623A7598A9.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 17}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphicsStateCollectionSerializerModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 18}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0F7364A96F72D420.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GraphViewModule.dll_0F7364A96F72D420.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0F7364A96F72D420.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 19}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GridAndSnapModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 20}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0BA6B28B7DDE48BD.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GridModule.dll_0BA6B28B7DDE48BD.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GridModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0BA6B28B7DDE48BD.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 21}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_759B0C885EE4B1A7.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.MultiplayerModule.dll_759B0C885EE4B1A7.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.MultiplayerModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_759B0C885EE4B1A7.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 22}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_74B8DFDA054CEE76.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.Physics2DModule.dll_74B8DFDA054CEE76.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.Physics2DModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_74B8DFDA054CEE76.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 23}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PhysicsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 24}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CAD99D8B71CB2C25.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.PresetsUIModule.dll_CAD99D8B71CB2C25.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CAD99D8B71CB2C25.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 25}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B8B976BE5D3312FA.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.PropertiesModule.dll_B8B976BE5D3312FA.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PropertiesModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B8B976BE5D3312FA.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 26}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B7CF8C9955F07FC2.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.QuickSearchModule.dll_B7CF8C9955F07FC2.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B7CF8C9955F07FC2.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 27}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_1490D3BA94AC08C7.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SafeModeModule.dll_1490D3BA94AC08C7.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SafeModeModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_1490D3BA94AC08C7.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 28}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0F72B2E67C862A83.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SceneTemplateModule.dll_0F72B2E67C862A83.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0F72B2E67C862A83.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 29}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4E2347B16495A12E.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SceneViewModule.dll_4E2347B16495A12E.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4E2347B16495A12E.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 30}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4E5283299F35A711.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.ShaderFoundryModule.dll_4E5283299F35A711.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.ShaderFoundryModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4E5283299F35A711.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 31}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FCA5B90ADFDDA4DE.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SketchUpModule.dll_FCA5B90ADFDDA4DE.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SketchUpModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FCA5B90ADFDDA4DE.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 32}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_74AAAA100BB74438.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SpriteMaskModule.dll_74AAAA100BB74438.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SpriteMaskModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_74AAAA100BB74438.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 33}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0F541D565BBF5C39.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SpriteShapeModule.dll_0F541D565BBF5C39.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SpriteShapeModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0F541D565BBF5C39.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 34}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_B952F182FF2AAC2D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SubstanceModule.dll_B952F182FF2AAC2D.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SubstanceModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_B952F182FF2AAC2D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 35}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_08DFEE18C9872BB2.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TerrainModule.dll_08DFEE18C9872BB2.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TerrainModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_08DFEE18C9872BB2.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 36}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D419680617AFED84.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TextCoreFontEngineModule.dll_D419680617AFED84.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D419680617AFED84.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 37}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1C019E77A733A6A2.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TextCoreTextEngineModule.dll_1C019E77A733A6A2.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1C019E77A733A6A2.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 38}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextRenderingModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 39}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A7DC5599E1440CBB.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TilemapModule.dll_A7DC5599E1440CBB.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TilemapModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A7DC5599E1440CBB.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 40}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8BE18BABED4F4805.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TreeModule.dll_8BE18BABED4F4805.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TreeModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8BE18BABED4F4805.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 41}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_1A31CBB193E1A822.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIAutomationModule.dll_1A31CBB193E1A822.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIAutomationModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_1A31CBB193E1A822.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 42}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_63FC11C5E22B4D54.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIBuilderModule.dll_63FC11C5E22B4D54.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_63FC11C5E22B4D54.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 43}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 44}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 45}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E0D0A358CEC9E306.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UmbraModule.dll_E0D0A358CEC9E306.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UmbraModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E0D0A358CEC9E306.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 46}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_64CAEA371C51C47C.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UnityConnectModule.dll_64CAEA371C51C47C.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_64CAEA371C51C47C.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 47}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.VFXModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 48}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_8B057BAB04355338.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.VideoModule.dll_8B057BAB04355338.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.VideoModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_8B057BAB04355338.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 49}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B4E8EB0C26028911.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.XRModule.dll_B4E8EB0C26028911.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.XRModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B4E8EB0C26028911.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 50}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_42181E7D7CFC5220.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AccessibilityModule.dll_42181E7D7CFC5220.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_42181E7D7CFC5220.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 51}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 52}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 53}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DDF8E0D249CFA8C6.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AnimationModule.dll_DDF8E0D249CFA8C6.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DDF8E0D249CFA8C6.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 54}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2BC5B9E767614323.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ARModule.dll_2BC5B9E767614323.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2BC5B9E767614323.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 55}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 56}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2C28B3A473206838.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AudioModule.dll_2C28B3A473206838.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2C28B3A473206838.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 57}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 58}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E4675F4CCDD99742.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClusterInputModule.dll_E4675F4CCDD99742.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E4675F4CCDD99742.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 59}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_73AD28DA03944D5F.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClusterRendererModule.dll_73AD28DA03944D5F.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_73AD28DA03944D5F.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 60}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_94CF18CB86D9DCA1.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ContentLoadModule.dll_94CF18CB86D9DCA1.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_94CF18CB86D9DCA1.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 61}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 62}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 63}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 64}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9C62264C713FC8D4.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.dll_9C62264C713FC8D4.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9C62264C713FC8D4.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 65}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 66}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8C2CA4C7EC8BC809.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GameCenterModule.dll_8C2CA4C7EC8BC809.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8C2CA4C7EC8BC809.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 67}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 68}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GraphicsStateCollectionSerializerModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 69}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_2EC785DE25E3B3E8.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GridModule.dll_2EC785DE25E3B3E8.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_2EC785DE25E3B3E8.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 70}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HierarchyCoreModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 71}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5F419ED82502C448.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.HotReloadModule.dll_5F419ED82502C448.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5F419ED82502C448.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 72}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 73}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 74}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputForUIModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 75}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 76}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 77}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A22FA7B97C37B2AA.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.JSONSerializeModule.dll_A22FA7B97C37B2AA.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A22FA7B97C37B2AA.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 78}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_22B4299F65558A3B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.LocalizationModule.dll_22B4299F65558A3B.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_22B4299F65558A3B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 79}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_A4215E1F9C7F6206.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.MarshallingModule.dll_A4215E1F9C7F6206.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.MarshallingModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_A4215E1F9C7F6206.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 80}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_7B60DC17711EC22C.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.MultiplayerModule.dll_7B60DC17711EC22C.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.MultiplayerModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_7B60DC17711EC22C.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 81}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_375D0AFEA61EDDB7.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ParticleSystemModule.dll_375D0AFEA61EDDB7.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_375D0AFEA61EDDB7.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 82}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_806F735295EEE3B0.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PerformanceReportingModule.dll_806F735295EEE3B0.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_806F735295EEE3B0.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 83}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2A9ED0C2EACCA05B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.Physics2DModule.dll_2A9ED0C2EACCA05B.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2A9ED0C2EACCA05B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 84}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_E1142B5458D7CA09.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PhysicsModule.dll_E1142B5458D7CA09.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_E1142B5458D7CA09.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 85}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 86}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 87}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BC17553E90A3DF0C.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ScreenCaptureModule.dll_BC17553E90A3DF0C.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BC17553E90A3DF0C.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 88}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_86E3ECAA28149898.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ShaderVariantAnalyticsModule.dll_86E3ECAA28149898.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ShaderVariantAnalyticsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_86E3ECAA28149898.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 89}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_63B0655FFCE700A3.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SharedInternalsModule.dll_63B0655FFCE700A3.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_63B0655FFCE700A3.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 90}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_755917580DFB5D83.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SpriteMaskModule.dll_755917580DFB5D83.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_755917580DFB5D83.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 91}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1519ECE56DF96CBE.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SpriteShapeModule.dll_1519ECE56DF96CBE.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1519ECE56DF96CBE.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 92}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 93}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6F38E26BE4AA5E12.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SubstanceModule.dll_6F38E26BE4AA5E12.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6F38E26BE4AA5E12.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 94}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E0067DB9DC434596.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SubsystemsModule.dll_E0067DB9DC434596.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E0067DB9DC434596.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 95}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 96}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 97}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_5278E3592AADAC4F.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextCoreFontEngineModule.dll_5278E3592AADAC4F.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_5278E3592AADAC4F.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 98}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 99}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E556C74947DB5F01.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextRenderingModule.dll_E556C74947DB5F01.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E556C74947DB5F01.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 100}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_9A570E444652F400.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TilemapModule.dll_9A570E444652F400.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_9A570E444652F400.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 101}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 102}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E444DF21DF6F818D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UIElementsModule.dll_E444DF21DF6F818D.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E444DF21DF6F818D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 103}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6DC69311668AEA06.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UIModule.dll_6DC69311668AEA06.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6DC69311668AEA06.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 104}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A6FD5FBD004713AB.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UmbraModule.dll_A6FD5FBD004713AB.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A6FD5FBD004713AB.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 105}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 106}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_56FC7BC852462FB3.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityAnalyticsModule.dll_56FC7BC852462FB3.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_56FC7BC852462FB3.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 107}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_522A6E4616D9C527.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityConnectModule.dll_522A6E4616D9C527.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_522A6E4616D9C527.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 108}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 109}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7E7EA495935A08A1.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityTestProtocolModule.dll_7E7EA495935A08A1.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7E7EA495935A08A1.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 110}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C72FD1A2212E60C4.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestAssetBundleModule.dll_C72FD1A2212E60C4.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C72FD1A2212E60C4.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 111}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F86BC7311C59AE48.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestAudioModule.dll_F86BC7311C59AE48.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F86BC7311C59AE48.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 112}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 113}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 114}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_65235F26A9F121D9.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestWWWModule.dll_65235F26A9F121D9.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_65235F26A9F121D9.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 115}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_876A8FBA92A0AFE9.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VehiclesModule.dll_876A8FBA92A0AFE9.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_876A8FBA92A0AFE9.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 116}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3ADE1BFBD2D45A0A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VFXModule.dll_3ADE1BFBD2D45A0A.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3ADE1BFBD2D45A0A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 117}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_5132380F35B283DD.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VideoModule.dll_5132380F35B283DD.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_5132380F35B283DD.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 118}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_512756ADC791754F.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VirtualTexturingModule.dll_512756ADC791754F.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_512756ADC791754F.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 119}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C098B99708456F7E.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VRModule.dll_C098B99708456F7E.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C098B99708456F7E.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 120}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 121}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F3577C9B917FBCDC.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.XRModule.dll_F3577C9B917FBCDC.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F3577C9B917FBCDC.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 122}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_617E8BB15AFBE74A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.WebGL.Extensions.dll_617E8BB15AFBE74A.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_617E8BB15AFBE74A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 123}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_36B61282F6B8813A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.WindowsStandalone.Extensions.dll_36B61282F6B8813A.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_36B61282F6B8813A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 124}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp", "DisplayName": "Writing Unity.Multiplayer.Center.Common.dll.mvfrm.rsp", "ActionType": "WriteFile", "PayloadOffset": 36146, "PayloadLength": 10314, "PayloadDebugContentSnippet": "Library\\Bee\\artifacts\\mvdfrm\\U", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"], "OutputFlags": [2], "DebugActionIndex": 125}, {"Annotation": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm\" \"@Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp\"", "Inputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_F0EC10369ED1F6F8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B5D72C560403CEFB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_6990DCC0F76DDF62.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_F8CF1649485C58E1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_2E35969D6589B2C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_6E00666893D93FDB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2CC00B8BFD198369.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_69BDDF7412CC3BEF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD7A8275FFD2CFCF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CC20AC623A7598A9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0F7364A96F72D420.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0BA6B28B7DDE48BD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_759B0C885EE4B1A7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_74B8DFDA054CEE76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CAD99D8B71CB2C25.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B8B976BE5D3312FA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B7CF8C9955F07FC2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_1490D3BA94AC08C7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0F72B2E67C862A83.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4E2347B16495A12E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4E5283299F35A711.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FCA5B90ADFDDA4DE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_74AAAA100BB74438.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0F541D565BBF5C39.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_B952F182FF2AAC2D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_08DFEE18C9872BB2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D419680617AFED84.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1C019E77A733A6A2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A7DC5599E1440CBB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8BE18BABED4F4805.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_1A31CBB193E1A822.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_63FC11C5E22B4D54.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E0D0A358CEC9E306.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_64CAEA371C51C47C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_8B057BAB04355338.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B4E8EB0C26028911.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_42181E7D7CFC5220.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DDF8E0D249CFA8C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2BC5B9E767614323.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2C28B3A473206838.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E4675F4CCDD99742.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_73AD28DA03944D5F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_94CF18CB86D9DCA1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9C62264C713FC8D4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8C2CA4C7EC8BC809.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_2EC785DE25E3B3E8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5F419ED82502C448.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A22FA7B97C37B2AA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_22B4299F65558A3B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_A4215E1F9C7F6206.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_7B60DC17711EC22C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_375D0AFEA61EDDB7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_806F735295EEE3B0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2A9ED0C2EACCA05B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_E1142B5458D7CA09.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BC17553E90A3DF0C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_86E3ECAA28149898.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_63B0655FFCE700A3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_755917580DFB5D83.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1519ECE56DF96CBE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6F38E26BE4AA5E12.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E0067DB9DC434596.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_5278E3592AADAC4F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E556C74947DB5F01.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_9A570E444652F400.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E444DF21DF6F818D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6DC69311668AEA06.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A6FD5FBD004713AB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_56FC7BC852462FB3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_522A6E4616D9C527.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7E7EA495935A08A1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C72FD1A2212E60C4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F86BC7311C59AE48.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_65235F26A9F121D9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_876A8FBA92A0AFE9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3ADE1BFBD2D45A0A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_5132380F35B283DD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_512756ADC791754F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C098B99708456F7E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F3577C9B917FBCDC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_617E8BB15AFBE74A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_36B61282F6B8813A.mvfrm", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125], "AllowUnexpectedOutput": true, "DebugActionIndex": 126}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt", "DisplayName": "Writing Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt", "ActionType": "WriteFile", "PayloadOffset": 46582, "PayloadLength": 147, "PayloadDebugContentSnippet": "C:\\Users\\<USER>\\Dow", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"], "OutputFlags": [2], "DebugActionIndex": 127}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp", "DisplayName": "Writing Unity.Multiplayer.Center.Editor.rsp", "ActionType": "WriteFile", "PayloadOffset": 46831, "PayloadLength": 40624, "PayloadDebugContentSnippet": "-target:library\n-out:\"Library/", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"], "OutputFlags": [2], "DebugActionIndex": 128}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2", "DisplayName": "Writing Unity.Multiplayer.Center.Editor.rsp2", "ActionType": "WriteFile", "PayloadOffset": 87558, "PayloadLength": 160, "PayloadDebugContentSnippet": "/pathmap:\"C:\\Users\\<USER>", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"], "OutputFlags": [2], "DebugActionIndex": 129}, {"Annotation": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)", "DisplayName": "Compiling C# (Unity.Multiplayer.Center.Editor)", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetCoreRuntime\\dotnet.exe\" exec \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp\" \"@Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEditor.Graphs.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/AnalyticsData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/AnalyticsUtils.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/DebugAnalytics.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/MultiplayerCenterAnalytics.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/MultiplayerCenterAnalyticsFactory.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/OnboardingSectionAnalyticsProvider.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/AssemblyInfo.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Features/PackageManagement.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/MultiplayerCenterWindow.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/RecommendationTabView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/RecommendationViewBottomBar.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/TabGroup.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionnaireView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionSection.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionViewFactory.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/PackageSelectionView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/RecommendationItemView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/RecommendationView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/SectionHeader.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/SolutionSelectionView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/StyleClasses.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/ViewUtils.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/GettingStartedTabView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/QuickstartPackageHandling.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/SectionsFinder.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/Logic.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/PresetData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireEditor.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireObject.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/UserChoicesObject.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/PreReleaseHandling.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationAuthoringData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationType.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationUtils.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationViewData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystem.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystemData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystemDataObject.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/Scoring.cs", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.pdb", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"], "OutputFlags": [0, 0, 0], "ToBuildDependencies": [4, 127, 128, 129, 135], "ToUseDependencies": [127, 129], "AllowUnexpectedOutput": true, "Env": [{"Key": "DOTNET_MULTILEVEL_LOOKUP", "Value": "0"}], "CachingMode": "ByDirectInputs", "DebugActionIndex": 130}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm\" \"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Common.ref.dll\"", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [4], "AllowUnexpectedOutput": true, "DebugActionIndex": 131}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_BB6FC027D536754A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AMDModule.dll_BB6FC027D536754A.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AMDModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_BB6FC027D536754A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 132}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_058B9A5D5D6FEA7D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.NVIDIAModule.dll_058B9A5D5D6FEA7D.mvfrm\" \"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_058B9A5D5D6FEA7D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 133}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp", "DisplayName": "Writing Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp", "ActionType": "WriteFile", "PayloadOffset": 87830, "PayloadLength": 10568, "PayloadDebugContentSnippet": "Library\\Bee\\artifacts\\mvdfrm\\U", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"], "OutputFlags": [2], "DebugActionIndex": 134}, {"Annotation": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm\" \"@Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp\"", "Inputs": ["Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_F0EC10369ED1F6F8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B5D72C560403CEFB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_6990DCC0F76DDF62.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_F8CF1649485C58E1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_2E35969D6589B2C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_6E00666893D93FDB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2CC00B8BFD198369.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_69BDDF7412CC3BEF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD7A8275FFD2CFCF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CC20AC623A7598A9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0F7364A96F72D420.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0BA6B28B7DDE48BD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_759B0C885EE4B1A7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_74B8DFDA054CEE76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CAD99D8B71CB2C25.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B8B976BE5D3312FA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B7CF8C9955F07FC2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_1490D3BA94AC08C7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0F72B2E67C862A83.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4E2347B16495A12E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4E5283299F35A711.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FCA5B90ADFDDA4DE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_74AAAA100BB74438.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0F541D565BBF5C39.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_B952F182FF2AAC2D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_08DFEE18C9872BB2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D419680617AFED84.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1C019E77A733A6A2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A7DC5599E1440CBB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8BE18BABED4F4805.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_1A31CBB193E1A822.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_63FC11C5E22B4D54.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E0D0A358CEC9E306.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_64CAEA371C51C47C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_8B057BAB04355338.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B4E8EB0C26028911.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_42181E7D7CFC5220.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_BB6FC027D536754A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DDF8E0D249CFA8C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2BC5B9E767614323.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2C28B3A473206838.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E4675F4CCDD99742.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_73AD28DA03944D5F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_94CF18CB86D9DCA1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9C62264C713FC8D4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8C2CA4C7EC8BC809.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_2EC785DE25E3B3E8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5F419ED82502C448.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A22FA7B97C37B2AA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_22B4299F65558A3B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_A4215E1F9C7F6206.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_7B60DC17711EC22C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_058B9A5D5D6FEA7D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_375D0AFEA61EDDB7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_806F735295EEE3B0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2A9ED0C2EACCA05B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_E1142B5458D7CA09.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BC17553E90A3DF0C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_86E3ECAA28149898.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_63B0655FFCE700A3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_755917580DFB5D83.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1519ECE56DF96CBE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6F38E26BE4AA5E12.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E0067DB9DC434596.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_5278E3592AADAC4F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E556C74947DB5F01.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_9A570E444652F400.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E444DF21DF6F818D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6DC69311668AEA06.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A6FD5FBD004713AB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_56FC7BC852462FB3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_522A6E4616D9C527.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7E7EA495935A08A1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C72FD1A2212E60C4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F86BC7311C59AE48.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_65235F26A9F121D9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_876A8FBA92A0AFE9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3ADE1BFBD2D45A0A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_5132380F35B283DD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_512756ADC791754F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C098B99708456F7E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F3577C9B917FBCDC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_617E8BB15AFBE74A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_36B61282F6B8813A.mvfrm", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 131, 132, 133, 134], "AllowUnexpectedOutput": true, "DebugActionIndex": 135}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt", "DisplayName": "Writing Assembly-CSharp.UnityAdditionalFile.txt", "ActionType": "WriteFile", "PayloadOffset": 98504, "PayloadLength": 147, "PayloadDebugContentSnippet": "C:\\Users\\<USER>\\Dow", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"], "OutputFlags": [2], "DebugActionIndex": 136}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp", "DisplayName": "Writing Assembly-CSharp.rsp", "ActionType": "WriteFile", "PayloadOffset": 98737, "PayloadLength": 34941, "PayloadDebugContentSnippet": "-target:library\n-out:\"Library/", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"], "OutputFlags": [2], "DebugActionIndex": 137}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2", "DisplayName": "Writing Assembly-CSharp.rsp2", "ActionType": "WriteFile", "PayloadOffset": 133765, "PayloadLength": 0, "PayloadDebugContentSnippet": "", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"], "OutputFlags": [2], "DebugActionIndex": 138}, {"Annotation": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)", "DisplayName": "Compiling C# (Assembly-CSharp)", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetCoreRuntime\\dotnet.exe\" exec \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp\" \"@Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll", "Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll", "Assets/Cainos/Pixel Art Top Down - Basic/Script/CameraFollow.cs", "Assets/Cainos/Pixel Art Top Down - Basic/Script/PropsAltar.cs", "Assets/Cainos/Pixel Art Top Down - Basic/Script/SpriteColorAnimation.cs", "Assets/Cainos/Pixel Art Top Down - Basic/Script/StairsLayerTrigger.cs", "Assets/Cainos/Pixel Art Top Down - Basic/Script/TopDownCharacterController.cs", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll", "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll", "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.pdb", "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.ref.dll"], "OutputFlags": [0, 0, 0], "ToBuildDependencies": [4, 130, 136, 137, 138, 142], "ToUseDependencies": [136, 138], "AllowUnexpectedOutput": true, "Env": [{"Key": "DOTNET_MULTILEVEL_LOOKUP", "Value": "0"}], "DebugActionIndex": 139}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library\\Bee\\artifacts\\mvdfrm\\Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm\" \"Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Multiplayer.Center.Editor.ref.dll\"", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [130], "AllowUnexpectedOutput": true, "DebugActionIndex": 140}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp", "DisplayName": "Writing Assembly-CSharp.dll.mvfrm.rsp", "ActionType": "WriteFile", "PayloadOffset": 133861, "PayloadLength": 10240, "PayloadDebugContentSnippet": "Library\\Bee\\artifacts\\mvdfrm\\U", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"], "OutputFlags": [2], "DebugActionIndex": 141}, {"Annotation": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm\" \"@Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp\"", "Inputs": ["Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm", "Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B5D72C560403CEFB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_6990DCC0F76DDF62.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_F8CF1649485C58E1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_2E35969D6589B2C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_6E00666893D93FDB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2CC00B8BFD198369.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_69BDDF7412CC3BEF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD7A8275FFD2CFCF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CC20AC623A7598A9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0F7364A96F72D420.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0BA6B28B7DDE48BD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_759B0C885EE4B1A7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_74B8DFDA054CEE76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CAD99D8B71CB2C25.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B8B976BE5D3312FA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B7CF8C9955F07FC2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_1490D3BA94AC08C7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0F72B2E67C862A83.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4E2347B16495A12E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4E5283299F35A711.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FCA5B90ADFDDA4DE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_74AAAA100BB74438.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0F541D565BBF5C39.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_B952F182FF2AAC2D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_08DFEE18C9872BB2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D419680617AFED84.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1C019E77A733A6A2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A7DC5599E1440CBB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8BE18BABED4F4805.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_1A31CBB193E1A822.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_63FC11C5E22B4D54.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E0D0A358CEC9E306.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_64CAEA371C51C47C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_8B057BAB04355338.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B4E8EB0C26028911.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_42181E7D7CFC5220.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DDF8E0D249CFA8C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2BC5B9E767614323.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2C28B3A473206838.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E4675F4CCDD99742.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_73AD28DA03944D5F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_94CF18CB86D9DCA1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9C62264C713FC8D4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8C2CA4C7EC8BC809.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_2EC785DE25E3B3E8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5F419ED82502C448.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A22FA7B97C37B2AA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_22B4299F65558A3B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_A4215E1F9C7F6206.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_7B60DC17711EC22C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_375D0AFEA61EDDB7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_806F735295EEE3B0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2A9ED0C2EACCA05B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_E1142B5458D7CA09.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BC17553E90A3DF0C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_86E3ECAA28149898.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_63B0655FFCE700A3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_755917580DFB5D83.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1519ECE56DF96CBE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6F38E26BE4AA5E12.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E0067DB9DC434596.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_5278E3592AADAC4F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E556C74947DB5F01.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_9A570E444652F400.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E444DF21DF6F818D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6DC69311668AEA06.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A6FD5FBD004713AB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_56FC7BC852462FB3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_522A6E4616D9C527.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7E7EA495935A08A1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C72FD1A2212E60C4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F86BC7311C59AE48.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_65235F26A9F121D9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_876A8FBA92A0AFE9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3ADE1BFBD2D45A0A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_5132380F35B283DD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_512756ADC791754F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C098B99708456F7E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F3577C9B917FBCDC.mvfrm", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 131, 140, 141], "AllowUnexpectedOutput": true, "DebugActionIndex": 142}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll", "DisplayName": "Copying Unity.Multiplayer.Center.Common.dll", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"], "OutputFlags": [2], "ToBuildDependencies": [4], "DebugActionIndex": 143}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb", "DisplayName": "Copying Unity.Multiplayer.Center.Common.pdb", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.pdb"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"], "OutputFlags": [2], "ToBuildDependencies": [4], "DebugActionIndex": 144}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll", "DisplayName": "Copying Unity.Multiplayer.Center.Editor.dll", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"], "OutputFlags": [2], "ToBuildDependencies": [130], "DebugActionIndex": 145}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb", "DisplayName": "Copying Unity.Multiplayer.Center.Editor.pdb", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.pdb"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"], "OutputFlags": [2], "ToBuildDependencies": [130], "DebugActionIndex": 146}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll", "DisplayName": "Copying Assembly-CSharp.dll", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Assembly-CSharp.dll"], "OutputFlags": [2], "ToBuildDependencies": [139], "DebugActionIndex": 147}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb", "DisplayName": "Copying Assembly-CSharp.pdb", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.pdb"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Assembly-CSharp.pdb"], "OutputFlags": [2], "ToBuildDependencies": [139], "DebugActionIndex": 148}, {"Annotation": "BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json", "DisplayName": "Extracting script serialization layouts", "Action": "\"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe\" -a=\"C:\\Users\\<USER>\\Downloads\\Engkwentro-The_Filipino_Myths_Chronicle (2)\\Engkwentro-The_Filipino_Myths_Chronicle\\Pixel Art Top Down - Basic\\Library\\ScriptAssemblies\\Assembly-CSharp.dll\" -a=\"C:\\Users\\<USER>\\Downloads\\Engkwentro-The_Filipino_Myths_Chronicle (2)\\Engkwentro-The_Filipino_Myths_Chronicle\\Pixel Art Top Down - Basic\\Library\\ScriptAssemblies\\Unity.Multiplayer.Center.Common.dll\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\Managed\\UnityEngine\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetStandard\\EditorExtensions\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\" -s=\"C:\\Users\\<USER>\\Downloads\\Engkwentro-The_Filipino_Myths_Chronicle (2)\\Engkwentro-The_Filipino_Myths_Chronicle\\Pixel Art Top Down - Basic\\Library\\ScriptAssemblies\" -o=\"Library/BuildPlayerData/Editor\" -rn=\"\" -tn=\"TypeDb-All.json\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/netcorerun/netcorerun.exe", "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe", "Library/ScriptAssemblies/Assembly-CSharp.dll", "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"], "InputFlags": [0, 0, 0, 0], "Outputs": ["Library/BuildPlayerData/Editor/TypeDb-All.json"], "OutputFlags": [0], "ToBuildDependencies": [143, 147], "AllowUnexpectedOutput": true, "DebugActionIndex": 149}, {"Annotation": "ScriptAssembliesAndTypeDB", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [6, 149], "DebugActionIndex": 150}], "FileSignatures": [{"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.BuildTools.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.Core.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.CSharpSupport.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.DotNet.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.NativeProgramSupport.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.Stevedore.Program.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.TinyProfiler2.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.GNU.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.LLVM.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.VisualStudio.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.Xcode.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.Tools.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.TundraBackend.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Bee.VisualStudioSolution.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/BeeLocalCacheTool.exe"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Newtonsoft.Json.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/NiceIO.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/SharpYaml.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.Api.Attributes.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Mdb.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Pdb.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Rocks.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Api.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.BuildLogic.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.Linker.Api.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/Unity.Options.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline/UnityEditor.iOS.Extensions.Xcode.dll"}, {"File": "Library/Bee/1900b0aE-inputdata.json"}], "StatSignatures": [{"File": "Assets/csc.rsp"}, {"File": "Assets/mcs.rsp"}, {"File": "Library/Bee/1900b0aE-inputdata.json"}, {"File": "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/csc.rsp"}, {"File": "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/csc.rsp"}], "GlobSignatures": [{"Path": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline"}], "ContentDigestExtensions": [".rsp", ".dll", ".exe", ".pdb", ".json", ".c<PERSON><PERSON>j"], "StructuredLogFileName": "Library/Bee/tundra.log.json", "StateFileName": "Library/Bee/TundraBuildState.state", "StateFileNameTmp": "Library/Bee/TundraBuildState.state.tmp", "StateFileNameMapped": "Library/Bee/TundraBuildState.state.map", "ScanCacheFileName": "Library/Bee/tundra.scancache", "ScanCacheFileNameTmp": "Library/Bee/tundra.scancache.tmp", "DigestCacheFileName": "Library/Bee/tundra.digestcache", "DigestCacheFileNameTmp": "Library/Bee/tundra.digestcache.tmp", "CachedNodeOutputDirectoryName": "Library/Bee/CachedNodeOutput", "EmitDataForBeeWhy": 0, "NamedNodes": {"all_tundra_nodes": 0, "ScriptAssemblies": 6, "ScriptAssembliesAndTypeDB": 150}, "DefaultNodes": [0], "SharedResources": [], "Scanners": [], "Identifier": "Library/Bee/1900b0aE.dag.json", "PayloadsFile": "C:/Users/<USER>/Downloads/Engkwen<PERSON>-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Pixel Art Top Down - Basic/Library/Bee/1900b0aE.dag.payloads", "RelativePathToRoot": "../.."}