{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 4108, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 4108, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 4108, "tid": 23, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 4108, "tid": 23, "ts": 1748782277944057, "dur": 27, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 4108, "tid": 23, "ts": 1748782277944109, "dur": 10, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 4108, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 4108, "tid": 1, "ts": 1748782277800498, "dur": 5170, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 4108, "tid": 1, "ts": 1748782277805682, "dur": 12504, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 4108, "tid": 1, "ts": 1748782277818191, "dur": 4601, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 4108, "tid": 23, "ts": 1748782277944122, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 4108, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277800330, "dur": 74947, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277875280, "dur": 67013, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277875306, "dur": 93, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277875414, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277875422, "dur": 1388, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277876819, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277876823, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277876942, "dur": 13, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277876959, "dur": 5180, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882155, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882163, "dur": 139, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882314, "dur": 5, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882321, "dur": 60, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882388, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882391, "dur": 129, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882527, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882531, "dur": 91, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882632, "dur": 4, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882638, "dur": 113, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882757, "dur": 2, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882760, "dur": 106, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882874, "dur": 6, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277882883, "dur": 522, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277883414, "dur": 3, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277883419, "dur": 228, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277883657, "dur": 4, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277883664, "dur": 111, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277883782, "dur": 2, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277883786, "dur": 96, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277883889, "dur": 7, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277883899, "dur": 154, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884064, "dur": 4, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884070, "dur": 119, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884197, "dur": 3, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884201, "dur": 80, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884288, "dur": 3, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884294, "dur": 81, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884380, "dur": 1, "ph": "X", "name": "ProcessMessages 95", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884387, "dur": 179, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884575, "dur": 4, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884582, "dur": 129, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884723, "dur": 4, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277884729, "dur": 263, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885002, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885006, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885130, "dur": 4, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885136, "dur": 103, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885248, "dur": 2, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885252, "dur": 107, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885363, "dur": 3, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885368, "dur": 106, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885482, "dur": 2, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885487, "dur": 115, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885609, "dur": 3, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885614, "dur": 81, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885700, "dur": 2, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885704, "dur": 38, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885745, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885747, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885837, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277885840, "dur": 165, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886016, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886020, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886123, "dur": 3, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886129, "dur": 171, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886308, "dur": 3, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886313, "dur": 93, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886415, "dur": 3, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886420, "dur": 87, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886511, "dur": 2, "ph": "X", "name": "ProcessMessages 97", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886515, "dur": 100, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886622, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886626, "dur": 81, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886712, "dur": 2, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886716, "dur": 94, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886817, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886821, "dur": 92, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886918, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277886922, "dur": 77, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887004, "dur": 2, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887007, "dur": 109, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887196, "dur": 3, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887201, "dur": 132, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887339, "dur": 3, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887344, "dur": 127, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887479, "dur": 3, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887485, "dur": 93, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887585, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887589, "dur": 107, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887701, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887704, "dur": 85, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887793, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887796, "dur": 118, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887921, "dur": 3, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277887925, "dur": 80, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888010, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888013, "dur": 69, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888086, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888089, "dur": 87, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888183, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888186, "dur": 89, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888280, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888284, "dur": 188, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888478, "dur": 2, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888482, "dur": 105, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888595, "dur": 3, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888600, "dur": 88, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888693, "dur": 2, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888696, "dur": 74, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888774, "dur": 1, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888777, "dur": 174, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888958, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277888962, "dur": 104, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889071, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889075, "dur": 181, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889266, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889271, "dur": 101, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889377, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889381, "dur": 194, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889582, "dur": 3, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889587, "dur": 106, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889697, "dur": 2, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889701, "dur": 227, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889936, "dur": 2, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277889940, "dur": 101, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890045, "dur": 2, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890049, "dur": 90, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890144, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890147, "dur": 195, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890349, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890352, "dur": 115, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890472, "dur": 3, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890476, "dur": 84, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890564, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890567, "dur": 88, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890662, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890667, "dur": 161, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890832, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890836, "dur": 108, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890950, "dur": 3, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277890956, "dur": 226, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891189, "dur": 2, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891194, "dur": 103, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891302, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891306, "dur": 81, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891391, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891394, "dur": 203, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891603, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891607, "dur": 114, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891727, "dur": 3, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891732, "dur": 86, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891822, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891824, "dur": 112, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891944, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277891949, "dur": 241, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892195, "dur": 3, "ph": "X", "name": "ProcessMessages 1148", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892200, "dur": 124, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892329, "dur": 1, "ph": "X", "name": "ProcessMessages 23", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892332, "dur": 73, "ph": "X", "name": "ReadAsync 23", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892409, "dur": 1, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892412, "dur": 100, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892519, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892523, "dur": 85, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892611, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892614, "dur": 208, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892827, "dur": 2, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892830, "dur": 72, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892907, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892909, "dur": 73, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892987, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277892990, "dur": 237, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277893233, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277893346, "dur": 95, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277893446, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277893450, "dur": 75, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277893531, "dur": 2, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277893535, "dur": 113, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277893655, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277893659, "dur": 415, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894191, "dur": 4, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894198, "dur": 223, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894425, "dur": 3, "ph": "X", "name": "ProcessMessages 1599", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894430, "dur": 248, "ph": "X", "name": "ReadAsync 1599", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894685, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894688, "dur": 103, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894797, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894801, "dur": 109, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894918, "dur": 2, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277894922, "dur": 100, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895031, "dur": 3, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895036, "dur": 165, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895209, "dur": 3, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895214, "dur": 249, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895470, "dur": 4, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895476, "dur": 379, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895863, "dur": 3, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895868, "dur": 106, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895983, "dur": 3, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277895987, "dur": 98, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277896093, "dur": 2, "ph": "X", "name": "ProcessMessages 226", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277896097, "dur": 87, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277896190, "dur": 2, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277896195, "dur": 101, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277896302, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277896307, "dur": 3294, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277899612, "dur": 2, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277899619, "dur": 384, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900010, "dur": 14, "ph": "X", "name": "ProcessMessages 8755", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900026, "dur": 82, "ph": "X", "name": "ReadAsync 8755", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900113, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900115, "dur": 72, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900192, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900195, "dur": 75, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900274, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900277, "dur": 70, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900352, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900354, "dur": 56, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900415, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900417, "dur": 124, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900548, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900552, "dur": 99, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900655, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277900659, "dur": 850, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901519, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901526, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901633, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901641, "dur": 86, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901735, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901741, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901823, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901826, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901903, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901908, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277901997, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902001, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902124, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902129, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902239, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902244, "dur": 85, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902335, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902339, "dur": 82, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902429, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902434, "dur": 77, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902517, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902522, "dur": 86, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902616, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902621, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902717, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902723, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902807, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902811, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902881, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902887, "dur": 73, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902968, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277902973, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903086, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903092, "dur": 81, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903180, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903185, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903264, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903269, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903321, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903324, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903385, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903388, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903477, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903484, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903559, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903562, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903662, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903668, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903753, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903758, "dur": 88, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903854, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903859, "dur": 120, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903986, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277903989, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904071, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904077, "dur": 75, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904157, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904161, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904238, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904243, "dur": 68, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904316, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904320, "dur": 159, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904487, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904491, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904568, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904571, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904654, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904659, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904764, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904768, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904844, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904847, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904930, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277904936, "dur": 88, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905031, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905036, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905121, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905124, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905194, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905197, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905275, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905280, "dur": 72, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905356, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905359, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905426, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905430, "dur": 67, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905501, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905504, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905596, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905601, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905681, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905685, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905766, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905772, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905890, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277905895, "dur": 99, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906007, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906016, "dur": 83, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906104, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906109, "dur": 74, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906189, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906194, "dur": 78, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906277, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906282, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906364, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906370, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906453, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277906457, "dur": 2316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277908783, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277908788, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277908892, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277908898, "dur": 269, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277909175, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277909179, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277909261, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277909264, "dur": 123, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277909396, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277909401, "dur": 412, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277909821, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277909826, "dur": 252, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277910087, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277910097, "dur": 731, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277910834, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277910838, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277910918, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277910921, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911034, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911038, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911136, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911139, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911232, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911237, "dur": 320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911567, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911572, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911672, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911675, "dur": 228, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911914, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911991, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277911995, "dur": 946, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277912950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277912954, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913048, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913052, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913172, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913177, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913275, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913279, "dur": 320, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913607, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913611, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913709, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277913713, "dur": 969, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277914689, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277914696, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277914779, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277914783, "dur": 154, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277914945, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277914949, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277915052, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277915056, "dur": 1163, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277916232, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277916237, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277916331, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 4108, "tid": 42949672960, "ts": 1748782277916336, "dur": 25939, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 4108, "tid": 23, "ts": 1748782277944148, "dur": 1330, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 4108, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 4108, "tid": 38654705664, "ts": 1748782277797090, "dur": 25766, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 4108, "tid": 38654705664, "ts": 1748782277822860, "dur": 52378, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 4108, "tid": 38654705664, "ts": 1748782277875242, "dur": 107, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 4108, "tid": 23, "ts": 1748782277945483, "dur": 12, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 4108, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 4108, "tid": 34359738368, "ts": 1748782277781840, "dur": 160551, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 4108, "tid": 34359738368, "ts": 1748782277782228, "dur": 14783, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 4108, "tid": 34359738368, "ts": 1748782277942399, "dur": 27, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 4108, "tid": 34359738368, "ts": 1748782277942429, "dur": 3, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 4108, "tid": 23, "ts": 1748782277945498, "dur": 30, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748782277875229, "dur": 361, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277875654, "dur": 3978, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277879653, "dur": 219, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277879953, "dur": 2223, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277882332, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1748782277882419, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_F0EC10369ED1F6F8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277882485, "dur": 205, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B5D72C560403CEFB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277882702, "dur": 146, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277882856, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_6990DCC0F76DDF62.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277882986, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_F8CF1649485C58E1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277883098, "dur": 166, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_2E35969D6589B2C6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277883276, "dur": 494, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_6E00666893D93FDB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277883779, "dur": 328, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2CC00B8BFD198369.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277884120, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_69BDDF7412CC3BEF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277884233, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277884370, "dur": 131, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD7A8275FFD2CFCF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277884512, "dur": 185, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CC20AC623A7598A9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277884706, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277884783, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0F7364A96F72D420.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277884844, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277885227, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0BA6B28B7DDE48BD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277885303, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_759B0C885EE4B1A7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277885366, "dur": 176, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_74B8DFDA054CEE76.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277885548, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277885633, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CAD99D8B71CB2C25.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277885759, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B8B976BE5D3312FA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277885823, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B7CF8C9955F07FC2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277885924, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_1490D3BA94AC08C7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277886059, "dur": 237, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0F72B2E67C862A83.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277886303, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4E2347B16495A12E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277886368, "dur": 219, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4E5283299F35A711.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277886600, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FCA5B90ADFDDA4DE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277886739, "dur": 197, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_74AAAA100BB74438.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277886946, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0F541D565BBF5C39.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277887037, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_B952F182FF2AAC2D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277887154, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_08DFEE18C9872BB2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277887300, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D419680617AFED84.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277887508, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1C019E77A733A6A2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277887584, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277887643, "dur": 170, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A7DC5599E1440CBB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277887824, "dur": 150, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8BE18BABED4F4805.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277887983, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_1A31CBB193E1A822.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277888112, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_63FC11C5E22B4D54.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277888232, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277888365, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277888496, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E0D0A358CEC9E306.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277888613, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_64CAEA371C51C47C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277888713, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277888797, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_8B057BAB04355338.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277888928, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B4E8EB0C26028911.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277889063, "dur": 140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_42181E7D7CFC5220.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277889213, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277889297, "dur": 127, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277889430, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DDF8E0D249CFA8C6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277889596, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2BC5B9E767614323.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277889718, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277889831, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2C28B3A473206838.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277889910, "dur": 234, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890152, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E4675F4CCDD99742.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890252, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_73AD28DA03944D5F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890389, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_94CF18CB86D9DCA1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890517, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890600, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890684, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890813, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9C62264C713FC8D4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890888, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277890964, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8C2CA4C7EC8BC809.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891025, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891098, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891165, "dur": 156, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_2EC785DE25E3B3E8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891327, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891524, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5F419ED82502C448.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891624, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891701, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891777, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891837, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891901, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277891962, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A22FA7B97C37B2AA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892104, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_22B4299F65558A3B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892165, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_A4215E1F9C7F6206.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892254, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_7B60DC17711EC22C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892316, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_375D0AFEA61EDDB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892379, "dur": 264, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_806F735295EEE3B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892653, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2A9ED0C2EACCA05B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892743, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_E1142B5458D7CA09.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892882, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277892968, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277893131, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BC17553E90A3DF0C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277893224, "dur": 190, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_86E3ECAA28149898.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277893424, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_63B0655FFCE700A3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277893590, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_755917580DFB5D83.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277893674, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1519ECE56DF96CBE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277893803, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277893888, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6F38E26BE4AA5E12.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277893976, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E0067DB9DC434596.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894061, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894197, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894289, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_5278E3592AADAC4F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894378, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894462, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E556C74947DB5F01.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894547, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_9A570E444652F400.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894645, "dur": 189, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894841, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E444DF21DF6F818D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277894924, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6DC69311668AEA06.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277895117, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A6FD5FBD004713AB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277895213, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277895347, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_56FC7BC852462FB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277895473, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_522A6E4616D9C527.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277895574, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277895667, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7E7EA495935A08A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277895813, "dur": 173, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C72FD1A2212E60C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277895992, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F86BC7311C59AE48.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277896083, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277896214, "dur": 216, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277896441, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_65235F26A9F121D9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277896542, "dur": 203, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_876A8FBA92A0AFE9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277896755, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3ADE1BFBD2D45A0A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277896855, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_5132380F35B283DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277896949, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_512756ADC791754F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897044, "dur": 119, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C098B99708456F7E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897259, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897363, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F3577C9B917FBCDC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897455, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_617E8BB15AFBE74A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897537, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_36B61282F6B8813A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897641, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_EE36537354EA42C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897738, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897862, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748782277897949, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748782277898049, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748782277898145, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277898258, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748782277898467, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_BB6FC027D536754A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277898565, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_058B9A5D5D6FEA7D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277898660, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277898769, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899033, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899140, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899244, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899337, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899445, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899636, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899712, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899776, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899833, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899895, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748782277899960, "dur": 368, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748782277900334, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748782277900427, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748782277900500, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748782277900576, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748782277900703, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748782277900817, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748782277882315, "dur": 18564, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277900883, "dur": 14462, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277915348, "dur": 370, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277915720, "dur": 159, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277915893, "dur": 131, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277916151, "dur": 103, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277916396, "dur": 8816, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748782277883658, "dur": 17253, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748782277904889, "dur": 1251, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1748782277900958, "dur": 5184, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748782277906143, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_BB6FC027D536754A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748782277906251, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748782277906363, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748782277906569, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748782277908342, "dur": 857, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.5f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748782277908006, "dur": 2450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748782277910457, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748782277911242, "dur": 2141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748782277913384, "dur": 1953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277883899, "dur": 17063, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277901038, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F3577C9B917FBCDC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748782277901863, "dur": 706, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277902606, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748782277902839, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277903141, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_11158203B9E39AB3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748782277903323, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748782277903604, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277903836, "dur": 595, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B967E4065F772C45.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748782277904434, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748782277904774, "dur": 1353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277906129, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_FBB8BF8AB85734B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748782277906213, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277906478, "dur": 3049, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748782277909530, "dur": 1828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277911359, "dur": 1951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748782277913311, "dur": 1933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277884247, "dur": 16782, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277901046, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748782277902196, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277902315, "dur": 1069, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3678F20D807E4058.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748782277903388, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_375D0AFEA61EDDB7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748782277903901, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277904045, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9C62264C713FC8D4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748782277904189, "dur": 869, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277905060, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9C62264C713FC8D4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748782277905272, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_1C019E77A733A6A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748782277905391, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277905531, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_74AAAA100BB74438.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748782277905813, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277906055, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748782277906121, "dur": 2876, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277908998, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748782277909098, "dur": 2278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277911377, "dur": 1903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748782277913280, "dur": 2032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277884387, "dur": 16670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277901077, "dur": 1105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0F7364A96F72D420.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277902184, "dur": 1094, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277903321, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277903481, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277903595, "dur": 184, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_D30204D88FDF4724.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277903783, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277904029, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277904320, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_DF9F268A85B90553.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277904397, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_2C28B3A473206838.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277904765, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277904885, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E0D0A358CEC9E306.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277904977, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277905177, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_1A31CBB193E1A822.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277905300, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277905556, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4E5283299F35A711.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277905647, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277905745, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CAD99D8B71CB2C25.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277905832, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277905939, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_36B61282F6B8813A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277906036, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277906133, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_36B61282F6B8813A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748782277906506, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277908470, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277908963, "dur": 2183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277911147, "dur": 2085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277913233, "dur": 1648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748782277914889, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277884470, "dur": 16609, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277901100, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277902409, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277902624, "dur": 1663, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_361646EF4766F2E3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277904289, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_73AD28DA03944D5F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277904363, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277904456, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2BC5B9E767614323.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277904761, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277904830, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_64CAEA371C51C47C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277904885, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277904967, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277905040, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277905106, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C2BBEEA3395EB34B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277905354, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_08DFEE18C9872BB2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277905431, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277905532, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_FCA5B90ADFDDA4DE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277905603, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277905678, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B7CF8C9955F07FC2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277905746, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277905826, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_759B0C885EE4B1A7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277905892, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277905966, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_F0EC10369ED1F6F8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277906038, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277906137, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_058B9A5D5D6FEA7D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748782277906213, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277906325, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277906618, "dur": 2891, "ph": "X", "name": "File", "args": {"detail": "Assets\\Cainos\\Pixel Art Top Down - Basic\\Script\\PropsAltar.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748782277906413, "dur": 4405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277910819, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277911213, "dur": 2010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277913231, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748782277913430, "dur": 1958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277884558, "dur": 16553, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277901122, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CC20AC623A7598A9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277901669, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277901786, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277901871, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277901933, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277902064, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1BAE3096AFD7820E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277902428, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_F86BC7311C59AE48.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277902519, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277902636, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_56FC7BC852462FB3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277902713, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277902806, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_9A570E444652F400.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277902880, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277902961, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_5278E3592AADAC4F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277903038, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277903125, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_755917580DFB5D83.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277903226, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277903343, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_E1142B5458D7CA09.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277903451, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277903545, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277903616, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277903708, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_929E7B54E9588CDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277903943, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277904024, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277904093, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0A8F3DF1A5F2CC74.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277904334, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277905075, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277905175, "dur": 1241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9311109F4D3D471C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748782277906418, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277908160, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277909610, "dur": 1724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277911335, "dur": 1937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748782277913273, "dur": 2161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277884621, "dur": 16596, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277901227, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD7A8275FFD2CFCF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277902091, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277902202, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3ADE1BFBD2D45A0A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277902659, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277902763, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E444DF21DF6F818D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277902915, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277903019, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_E0067DB9DC434596.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277903098, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277903257, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BC17553E90A3DF0C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277903333, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277903411, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_A4215E1F9C7F6206.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277903490, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277903586, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277903660, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277903729, "dur": 532, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6488530B3951FC19.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277904264, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_94CF18CB86D9DCA1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277904387, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277904487, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277904951, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277905041, "dur": 4419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_7252F4E40A43E45A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748782277909464, "dur": 1669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277911139, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277911311, "dur": 2020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748782277913331, "dur": 2096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277884740, "dur": 16511, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277901266, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277901954, "dur": 1137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277903093, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EBD342287D99804B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277903167, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_63B0655FFCE700A3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277903361, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277903529, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_A22FA7B97C37B2AA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277903621, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277903874, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277904000, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277904093, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B932BB57EBA9AB16.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277904146, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277904243, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277904327, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_515669F00D8F1DF1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277904508, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_42181E7D7CFC5220.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277905215, "dur": 4367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277909599, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277909891, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277910059, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748782277910769, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277911154, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277911713, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277911939, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277912099, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748782277912888, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277913254, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277913396, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_EB1F4B6E56116D4A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277913518, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748782277913722, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277913872, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748782277914562, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277914868, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748782277914931, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277884893, "dur": 16502, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277901419, "dur": 839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_69BDDF7412CC3BEF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277902260, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277902528, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7E7EA495935A08A1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277902636, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277902752, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A6FD5FBD004713AB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277902841, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277903010, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277903107, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277903189, "dur": 805, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_FB5AA6C32E9612F7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277903996, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277904137, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277904205, "dur": 376, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_14F8F7A48AD7B16F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277904583, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_8B057BAB04355338.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277904972, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277905196, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_8BE18BABED4F4805.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277905426, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277905609, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0F72B2E67C862A83.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277905698, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277905797, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277906002, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277906157, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_EE9972A9F3C8E2C4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748782277906543, "dur": 1994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277908665, "dur": 2476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277911151, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277911319, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1748782277911428, "dur": 1833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748782277913262, "dur": 2183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277885183, "dur": 16259, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277901465, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2CC00B8BFD198369.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277901685, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277901824, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277901923, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277902063, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_78E41CCF221529E7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277902127, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_5132380F35B283DD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277902206, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277902291, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_876A8FBA92A0AFE9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277903011, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277903099, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_1519ECE56DF96CBE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277903208, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277903349, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2A9ED0C2EACCA05B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277903786, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277903883, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_2EC785DE25E3B3E8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277903952, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277904054, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277904146, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277904213, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0FA7963B1876E04E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277904459, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DDF8E0D249CFA8C6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277905139, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277905239, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_A7DC5599E1440CBB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277905313, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277905401, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_B952F182FF2AAC2D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277905467, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277905567, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4E2347B16495A12E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277905639, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277905715, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B8B976BE5D3312FA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277905783, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277905863, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_0BA6B28B7DDE48BD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748782277905930, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277906026, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277906120, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277906208, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748782277906274, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277906355, "dur": 363, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748782277906719, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277907981, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277909407, "dur": 1755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277911163, "dur": 2065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277913236, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748782277913430, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748782277913485, "dur": 1755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277885363, "dur": 16136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277901506, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_6E00666893D93FDB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277902173, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277902327, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_65235F26A9F121D9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277902500, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277902617, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_522A6E4616D9C527.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277902726, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277902817, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E556C74947DB5F01.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277902890, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277902977, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277903050, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277903127, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_37D7B3D1BF61D844.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277903396, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_7B60DC17711EC22C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277903480, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277903574, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277903643, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277903710, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8EDEFEE14CAC64C5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277903959, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_8C2CA4C7EC8BC809.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277904046, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277904145, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277904221, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277904293, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_E44AC1B2380D5229.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277904484, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277904898, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277905009, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_61DF5AAD264AD718.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277905151, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_63FC11C5E22B4D54.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277905236, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277905321, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_D419680617AFED84.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277905397, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277905504, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0F541D565BBF5C39.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277905578, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277905663, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_1490D3BA94AC08C7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277905731, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277905808, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_74B8DFDA054CEE76.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277905876, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277905951, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_617E8BB15AFBE74A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748782277906025, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277906110, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277906213, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277906392, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277906507, "dur": 3044, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748782277909554, "dur": 1837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277911392, "dur": 1851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748782277913243, "dur": 2301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277885468, "dur": 16109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277901587, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_2E35969D6589B2C6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277901711, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277901800, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_6990DCC0F76DDF62.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277901867, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277902083, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C098B99708456F7E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277902217, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277902447, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_C72FD1A2212E60C4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277902537, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277902655, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B5D72C560403CEFB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277902743, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277902828, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277902897, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277902966, "dur": 410, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_9A6119CABA31656D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277903378, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_806F735295EEE3B0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277903461, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277903560, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277903635, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277903707, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_8CFDDAA95048EAA5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277903910, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277905021, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277905108, "dur": 1487, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_3F0A13DF14663C48.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748782277906598, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277907975, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277909401, "dur": 1800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277911202, "dur": 2033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748782277913235, "dur": 2355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277885688, "dur": 15915, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277901672, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277902045, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277902196, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277902295, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_C7A545189EC59C5A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277902412, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277902536, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277902719, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F13E2E3490AD9FD2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277902785, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277902883, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277902971, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_B9FF838CFC66F09D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277903049, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6F38E26BE4AA5E12.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277903147, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277903245, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_86E3ECAA28149898.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277903332, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277903442, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_22B4299F65558A3B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277904010, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277904305, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_E4675F4CCDD99742.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277904433, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277904537, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B4E8EB0C26028911.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277904864, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277904988, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277905103, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277905193, "dur": 1444, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E39671D9DD2B6842.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748782277906640, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277908419, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277910010, "dur": 1296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277911307, "dur": 2073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748782277913381, "dur": 1869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277885808, "dur": 15935, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277901747, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_F8CF1649485C58E1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277901940, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277902104, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_512756ADC791754F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277902207, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277902341, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277902546, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277902704, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C629CEF97C485D1D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277902759, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6DC69311668AEA06.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277902931, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277903073, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277903172, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277903260, "dur": 575, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_928DCE2299576004.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277903839, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5F419ED82502C448.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277903961, "dur": 771, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277904773, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277905082, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277905169, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2EBC79A863244C05.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277905247, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277905417, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277905509, "dur": 947, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_5915D521CEE2DBDC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748782277906459, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277908198, "dur": 1619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277909818, "dur": 1352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277911172, "dur": 2132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748782277913305, "dur": 2004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748782277932268, "dur": 3720, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 4108, "tid": 23, "ts": 1748782277945616, "dur": 6047, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 4108, "tid": 23, "ts": 1748782277951925, "dur": 2527, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 4108, "tid": 23, "ts": 1748782277944089, "dur": 10459, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}