Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.5f1 (923722cbbcfc) revision 9582370'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 15897 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-01T12:36:32Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.5f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Users/<USER>/Downloads/Engkwentro-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Engkwentro-The_Filipino_Myths_Chronicle
-logFile
Logs/AssetImportWorker1.log
-srvPort
50488
-job-worker-count
6
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Downloads/Engkwentro-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Engkwentro-The_Filipino_Myths_Chronicle
C:/Users/<USER>/Downloads/Engkwentro-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Engkwentro-The_Filipino_Myths_Chronicle
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21940]  Target information:

Player connection [21940]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 1362105381 [EditorId] 1362105381 [Version] 1048832 [Id] WindowsEditor(7,SSI-JSantonia-EBG) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21940] Host joined multi-casting on [***********:54997]...
Player connection [21940] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 6
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 28.14 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.5f1 (923722cbbcfc)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Downloads/Engkwentro-The_Filipino_Myths_Chronicle (2)/Engkwentro-The_Filipino_Myths_Chronicle/Engkwentro-The_Filipino_Myths_Chronicle/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) Graphics (ID=0x7d45)
    Vendor:   Intel
    VRAM:     9061 MB
    Driver:   32.0.101.6556
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56116
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.009889 seconds.
- Loaded All Assemblies, in  1.117 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.021 seconds
Domain Reload Profiling: 2130ms
	BeginReloadAssembly (345ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (112ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (149ms)
	LoadAllAssembliesAndSetupDomain (470ms)
		LoadAssemblies (342ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (457ms)
			TypeCache.Refresh (454ms)
				TypeCache.ScanAssembly (419ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1022ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (916ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (146ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (200ms)
			ProcessInitializeOnLoadAttributes (390ms)
			ProcessInitializeOnLoadMethodAttributes (171ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
DirectoryNotFoundException: Could not find a part of the path "C:\Users\<USER>\Downloads\Engkwentro-The_Filipino_Myths_Chronicle (2)\Engkwentro-The_Filipino_Myths_Chronicle\Engkwentro-The_Filipino_Myths_Chronicle\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll"
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)
  at Mono.Cecil.ModuleDefinition.GetFileStream (System.String fileName, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share) [0x00006] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at Mono.Cecil.ModuleDefinition.ReadModule (System.String fileName, Mono.Cecil.ReaderParameters parameters) [0x00008] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at Mono.Cecil.AssemblyDefinition.ReadAssembly (System.String fileName, Mono.Cecil.ReaderParameters parameters) [0x00000] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at UnityEditor.AssemblyValidation.LoadAssemblyDefinitions (System.String[] assemblyPaths, System.String[] searchPaths) [0x00048] in <1d4abd3cd5ee415ab29235fc867cd8e6>:0 
  at UnityEditor.AssemblyValidation.ValidateAssemblies (System.String[] assemblyPaths, System.Boolean enableLogging) [0x00007] in <1d4abd3cd5ee415ab29235fc867cd8e6>:0 

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.884 seconds
Refreshing native plugins compatible for Editor in 15.90 ms, found 2 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 33.36 ms, found 2 plugins.
System.IO.DirectoryNotFoundException: Could not find a part of the path "C:\Users\<USER>\Downloads\Engkwentro-The_Filipino_Myths_Chronicle (2)\Engkwentro-The_Filipino_Myths_Chronicle\Engkwentro-The_Filipino_Myths_Chronicle\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\localization\plastic-gui.en.txt"
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean isAsync, System.Boolean anonymous) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess)
  at Codice.Utils.LocalizationTranslator`1+FromTxt[T].GetTranslationsFromFile (System.String file, System.Boolean bLogExceptions) [0x0001b] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Codice.Utils.LocalizationTranslator`1[T].BuildFromLocalizationFileOnDir (System.String localizationDirPath, System.String fileName, System.String culture) [0x00089] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Codice.Utils.LocalizationTranslator`1[T].BuildFromLocalizationFile (System.String fileName, System.String culture) [0x00006] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at PlasticGui.PlasticLocalization.GetTranslator (System.String fileName, System.String culture) [0x00011] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at PlasticGui.PlasticLocalization.GetString (PlasticGui.PlasticLocalization+Name name) [0x00001] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Unity.Cloud.Collaborate.ToolbarButton..ctor () [0x00017] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:30 
  at Unity.Cloud.Collaborate.ToolbarButton.InitializeIfNeeded () [0x00000] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:22 
  at Unity.Cloud.Collaborate.ToolbarBootstrap..cctor () [0x00000] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:14 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.897 seconds
Domain Reload Profiling: 3764ms
	BeginReloadAssembly (554ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (179ms)
	RebuildNativeTypeToScriptingClass (51ms)
	initialDomainReloadingComplete (164ms)
	LoadAllAssembliesAndSetupDomain (918ms)
		LoadAssemblies (809ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (474ms)
			TypeCache.Refresh (313ms)
				TypeCache.ScanAssembly (265ms)
			BuildScriptInfoCaches (146ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1899ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1629ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (374ms)
			ProcessInitializeOnLoadAttributes (722ms)
			ProcessInitializeOnLoadMethodAttributes (501ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 20.21 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 2838 unused Assets / (1.0 MB). Loaded Objects now: 3301.
Memory consumption went from 81.3 MB to 80.3 MB.
Total: 27.223400 ms (FindLiveObjects: 0.993500 ms CreateObjectMapping: 0.788900 ms MarkObjects: 22.617800 ms  DeleteObjects: 2.819000 ms)

========================================================================
Received Import Request.
  Time since last request: 230164.480553 seconds.
  path: Assets/Artwork/Animations/enemy/Bosses/BigDemon/Boss_Big_Demon_Idle.anim
  artifactKey: Guid(7c6fc5057385fe347a5c71b0ae8829a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Animations/enemy/Bosses/BigDemon/Boss_Big_Demon_Idle.anim using Guid(7c6fc5057385fe347a5c71b0ae8829a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '59b5878e41944f3b767b73957edc576a') in 0.0651913 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.001489 seconds.
  path: Assets/Artwork/Animations/enemy/Bosses/BigDemon
  artifactKey: Guid(bceabb0e2c1ee1e41af5d6fd8a758159) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Animations/enemy/Bosses/BigDemon using Guid(bceabb0e2c1ee1e41af5d6fd8a758159) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '174bf2c0dfbc238b2fca611709042be2') in 0.0011748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000208 seconds.
  path: Assets/Artwork/Animations/Canvas/NPC_Dialogue_OverrideAnimator.overrideController
  artifactKey: Guid(efa74aeca5cefbc43a2e83304e1516fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Animations/Canvas/NPC_Dialogue_OverrideAnimator.overrideController using Guid(efa74aeca5cefbc43a2e83304e1516fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '75fb2c9fad3cc54de5ba2ef84418a900') in 0.0119902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Artwork/Animations/enemy/Bosses
  artifactKey: Guid(4f79f289a22d1564d87390dbf6979d52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Animations/enemy/Bosses using Guid(4f79f289a22d1564d87390dbf6979d52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc9d0c2ec0952fb3ddb2471e16c10b1a') in 0.0022531 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000199 seconds.
  path: Assets/Artwork/Animations/enemy/Bosses/BigDemon/Boss_One_Eye.overrideController
  artifactKey: Guid(5ad2ed890f21ae14fb0cbcb1f5f7a2ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Animations/enemy/Bosses/BigDemon/Boss_One_Eye.overrideController using Guid(5ad2ed890f21ae14fb0cbcb1f5f7a2ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1c973826a3aafc2dff4c20fb5b528be2') in 0.008637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
DirectoryNotFoundException: Could not find a part of the path "C:\Users\<USER>\Downloads\Engkwentro-The_Filipino_Myths_Chronicle (2)\Engkwentro-The_Filipino_Myths_Chronicle\Engkwentro-The_Filipino_Myths_Chronicle\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll"
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare)
  at Mono.Cecil.ModuleDefinition.GetFileStream (System.String fileName, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share) [0x00006] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at Mono.Cecil.ModuleDefinition.ReadModule (System.String fileName, Mono.Cecil.ReaderParameters parameters) [0x00008] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at Mono.Cecil.AssemblyDefinition.ReadAssembly (System.String fileName, Mono.Cecil.ReaderParameters parameters) [0x00000] in <a6860a9f6366437387ebdc1f225b7fd4>:0 
  at UnityEditor.AssemblyValidation.LoadAssemblyDefinitions (System.String[] assemblyPaths, System.String[] searchPaths) [0x00048] in <1d4abd3cd5ee415ab29235fc867cd8e6>:0 
  at UnityEditor.AssemblyValidation.ValidateAssemblies (System.String[] assemblyPaths, System.Boolean enableLogging) [0x00007] in <1d4abd3cd5ee415ab29235fc867cd8e6>:0 

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.206 seconds
Refreshing native plugins compatible for Editor in 17.50 ms, found 2 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
System.IO.DirectoryNotFoundException: Could not find a part of the path "C:\Users\<USER>\Downloads\Engkwentro-The_Filipino_Myths_Chronicle (2)\Engkwentro-The_Filipino_Myths_Chronicle\Engkwentro-The_Filipino_Myths_Chronicle\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\localization\plastic-gui.en.txt"
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x0019e] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean isAsync, System.Boolean anonymous) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access) [0x00000] in <71119615d44348f087b10ce3c1671c84>:0 
  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess)
  at Codice.Utils.LocalizationTranslator`1+FromTxt[T].GetTranslationsFromFile (System.String file, System.Boolean bLogExceptions) [0x0001b] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Codice.Utils.LocalizationTranslator`1[T].BuildFromLocalizationFileOnDir (System.String localizationDirPath, System.String fileName, System.String culture) [0x00089] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Codice.Utils.LocalizationTranslator`1[T].BuildFromLocalizationFile (System.String fileName, System.String culture) [0x00006] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at PlasticGui.PlasticLocalization.GetTranslator (System.String fileName, System.String culture) [0x00011] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at PlasticGui.PlasticLocalization.GetString (PlasticGui.PlasticLocalization+Name name) [0x00001] in <54da4867cd52476e8489fcedbed1f8e5>:0 
  at Unity.Cloud.Collaborate.ToolbarButton..ctor () [0x00017] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:30 
  at Unity.Cloud.Collaborate.ToolbarButton.InitializeIfNeeded () [0x00000] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:22 
  at Unity.Cloud.Collaborate.ToolbarBootstrap..cctor () [0x00000] in .\Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Editor\Toolbar\ToolbarButton.cs:14 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.813 seconds
Domain Reload Profiling: 3013ms
	BeginReloadAssembly (454ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (88ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (560ms)
		LoadAssemblies (579ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (211ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (186ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1815ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1476ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (301ms)
			ProcessInitializeOnLoadAttributes (541ms)
			ProcessInitializeOnLoadMethodAttributes (589ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 28.58 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Unloading 2836 unused Assets / (0.9 MB). Loaded Objects now: 3304.
Memory consumption went from 80.2 MB to 79.2 MB.
Total: 30.735100 ms (FindLiveObjects: 0.968500 ms CreateObjectMapping: 0.554900 ms MarkObjects: 26.270100 ms  DeleteObjects: 2.937400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 481.139082 seconds.
  path: Assets/Prefabs/Enemies/Ranged/BittenVonKrumpen.prefab
  artifactKey: Guid(102e2e8642ed61844b9e1b75239e33f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Ranged/BittenVonKrumpen.prefab using Guid(102e2e8642ed61844b9e1b75239e33f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 20.54 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'c2f2817b17c1ec9c5cfb8d53b32fddcf') in 0.4679843 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 116

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Prefabs/Others/SceneDoorportal.prefab
  artifactKey: Guid(dc99061e059f8154baf709e1b501191c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/SceneDoorportal.prefab using Guid(dc99061e059f8154baf709e1b501191c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2366390352c74a4b183a33ff044e96d2') in 0.037276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Prefabs/BlockedDoors/EnableEnemyTarget.prefab
  artifactKey: Guid(01358ebe10d5ee94795c54ce3f03732c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BlockedDoors/EnableEnemyTarget.prefab using Guid(01358ebe10d5ee94795c54ce3f03732c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46b1aec852f2ad9d57083b9d72c156c9') in 0.0060666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000091 seconds.
  path: Assets/Prefabs/Others/Chest.prefab
  artifactKey: Guid(67ce4a30917456147843a68beed8bdc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/Chest.prefab using Guid(67ce4a30917456147843a68beed8bdc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1c40cac2ff159bc2e4da6c0363714ba4') in 0.0296454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Prefabs/Enemies/Ranged/MinotaurAxeProjectile.prefab
  artifactKey: Guid(32ecaaf17381c1c498e23f7168fc3b52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Ranged/MinotaurAxeProjectile.prefab using Guid(32ecaaf17381c1c498e23f7168fc3b52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '862d16a9069b7408bad5e650e46f6baf') in 0.0337321 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_Swampy.prefab
  artifactKey: Guid(b2ff70f1dd64c7e489f2c478575b701d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_Swampy.prefab using Guid(b2ff70f1dd64c7e489f2c478575b701d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 17.97 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '05b0693cf10968fe99135684dac732ba') in 0.0865561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_Muddy.prefab
  artifactKey: Guid(1e4c3cb893b956340acfb548242b526f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_Muddy.prefab using Guid(1e4c3cb893b956340acfb548242b526f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 21.47 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '5e786940d99d51805059591d700f17c0') in 0.0844465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Prefabs/GameManager.prefab
  artifactKey: Guid(2a2cdba92a379e34bb96a1e785052265) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/GameManager.prefab using Guid(2a2cdba92a379e34bb96a1e785052265) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '22732d2fcc5fcd18af9dfd1eba54d9bd') in 0.1350394 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 175

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Prefabs/Others/SingleCrate.prefab
  artifactKey: Guid(4b06331aafae7984b8d537a05a239b1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/SingleCrate.prefab using Guid(4b06331aafae7984b8d537a05a239b1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15b53b48ec926eb3bfee695d7e799256') in 0.0271917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Artwork/Level/Dungeon.prefab
  artifactKey: Guid(7e0ac77e65d806844a70a9d103102415) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Level/Dungeon.prefab using Guid(7e0ac77e65d806844a70a9d103102415) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58ef2ecb9c8cce1e9edad336a446d6e4') in 0.3837705 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 255

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Prefabs/BlockedDoors/EnemyBatch1.prefab
  artifactKey: Guid(a8633431474b3c44c94c9210dcd542b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BlockedDoors/EnemyBatch1.prefab using Guid(a8633431474b3c44c94c9210dcd542b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 29.31 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '8fd7a45dc48524cb399f170199317cc6') in 0.1722721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 196

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/Prefabs/BlockedDoors/blocked_orange_front.prefab
  artifactKey: Guid(a1eeb8e968a8f814faebbfd538d58595) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BlockedDoors/blocked_orange_front.prefab using Guid(a1eeb8e968a8f814faebbfd538d58595) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '707484d2d0433b5a791a8537c8750a7c') in 0.0249557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Resources/flamethrower_bullet.prefab
  artifactKey: Guid(4894318d18dc240488aabdb64d057a45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/flamethrower_bullet.prefab using Guid(4894318d18dc240488aabdb64d057a45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95f0a478165fac623c8fa41b5b2c283d') in 0.0288397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Prefabs/player_1.prefab
  artifactKey: Guid(747d9e9107ad8304b85dfa39402e88dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/player_1.prefab using Guid(747d9e9107ad8304b85dfa39402e88dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '********************************') in 0.0440628 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 42

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Prefabs/Weapons/Matter.prefab
  artifactKey: Guid(7d133083e5bf41346b2d0a9737beb88a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/Matter.prefab using Guid(7d133083e5bf41346b2d0a9737beb88a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7c4b91d243639509d8dc9098601313b') in 0.0129766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Prefabs/Bullets/bullet_tomato.prefab
  artifactKey: Guid(3094c5a43da04324c9cd6cb9ad754699) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Bullets/bullet_tomato.prefab using Guid(3094c5a43da04324c9cd6cb9ad754699) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8876eeea0fc5652a5010fccecafa447f') in 0.0691349 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Resources/black_bullet.prefab
  artifactKey: Guid(c5f38467259c7884f9130a6d285be700) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/black_bullet.prefab using Guid(c5f38467259c7884f9130a6d285be700) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '40baad3261219d4c2abfd9c41b77f7d6') in 0.0322345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Prefabs/Weapons/MG.prefab
  artifactKey: Guid(e86940c1640320a4da8b4e207c92095b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/MG.prefab using Guid(e86940c1640320a4da8b4e207c92095b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3dfa5aa093ae0828ee5e573a4a37cbdd') in 0.0094024 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Prefabs/Others/DoubleCrate.prefab
  artifactKey: Guid(8836ef4c6d139e04793ff4161459f6a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/DoubleCrate.prefab using Guid(8836ef4c6d139e04793ff4161459f6a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e47fd738d55c456f6402b498cae0244') in 0.0253813 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Prefabs/Decorations/decor_water_fountain.prefab
  artifactKey: Guid(6a08da4b8f5198e49974406cc11e46c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Decorations/decor_water_fountain.prefab using Guid(6a08da4b8f5198e49974406cc11e46c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '48c22a53ca41853e72c4f930a91dff33') in 0.0357974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Resources/rocket_bullet.prefab
  artifactKey: Guid(b982b90e79d2e50448b3e42dd3eafec9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/rocket_bullet.prefab using Guid(b982b90e79d2e50448b3e42dd3eafec9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a8bf37b0ef48850b5174a7bf5300051') in 0.0351615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Prefabs/Others/NPC/NPC_Fighter-F.prefab
  artifactKey: Guid(00e47d7a15324a94aa69074f52b4208f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/NPC/NPC_Fighter-F.prefab using Guid(00e47d7a15324a94aa69074f52b4208f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bdd054b94bbc6887acba33b3df3db86d') in 0.0381411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Prefabs/Enemies/Ranged/BittenVonKrumpenProjectile.prefab
  artifactKey: Guid(3e87a4963ea576d4f825f6dd63adc07e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Ranged/BittenVonKrumpenProjectile.prefab using Guid(3e87a4963ea576d4f825f6dd63adc07e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b61bc63b58d37cffdb012c6e5563d3d3') in 0.032394 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Prefabs/Weapons/Pistol.prefab
  artifactKey: Guid(741a450e69f432f44ad98b289a8e1e44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/Pistol.prefab using Guid(741a450e69f432f44ad98b289a8e1e44) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c76cbb3a74db216c2863ee3661149a9') in 0.0151761 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Prefabs/Others/FloatingText.prefab
  artifactKey: Guid(a893ae296439efb4cbf74613234b01f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/FloatingText.prefab using Guid(a893ae296439efb4cbf74613234b01f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '474813dee79c1d6bb4e2b957497d9c22') in 0.0171602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Prefabs/Others/NPC/NPC_Townfolk-Adult-M.prefab
  artifactKey: Guid(6665c120dc63c0942897786fd07919c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/NPC/NPC_Townfolk-Adult-M.prefab using Guid(6665c120dc63c0942897786fd07919c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b80aca4c570a5a2fbbb60a2c19438c26') in 0.0366278 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Prefabs/Weapons/Canon.prefab
  artifactKey: Guid(0b3a7c768f709d44ba8e412f141337f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/Canon.prefab using Guid(0b3a7c768f709d44ba8e412f141337f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d08958a0f3b312cca93b91989c77196') in 0.0143023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Resources/orange_bullet.prefab
  artifactKey: Guid(d886c5d13387f7e4eaa14730c61107ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/orange_bullet.prefab using Guid(d886c5d13387f7e4eaa14730c61107ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '98042ac4f59046be49b50d95577ef7ca') in 0.0293448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Prefabs/Enemies/Boses/Boss_One_Eye.prefab
  artifactKey: Guid(678e4e2b51518904b8855aa9af4daecc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Boses/Boss_One_Eye.prefab using Guid(678e4e2b51518904b8855aa9af4daecc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 23.83 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'bf29c414710fbb615ec52205785d94e8') in 0.0636199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Prefabs/Enemies/Ranged/Devil_Projectile.prefab
  artifactKey: Guid(28b08a3d1689a16408d5c5b8dc7e3ee7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Ranged/Devil_Projectile.prefab using Guid(28b08a3d1689a16408d5c5b8dc7e3ee7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0175b5e2879d02dd69de5216745450d1') in 0.0579982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Prefabs/Weapons/Spazer.prefab
  artifactKey: Guid(97933ebb7bea8bc49afe5420e168bd67) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/Spazer.prefab using Guid(97933ebb7bea8bc49afe5420e168bd67) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e66dd2cc1d81dfc55e640706ff7921ca') in 0.0128955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Prefabs/UI/Canvas_CharacterMenu.prefab
  artifactKey: Guid(786fc68587af13d46a0259487076c014) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/UI/Canvas_CharacterMenu.prefab using Guid(786fc68587af13d46a0259487076c014) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3961e52ee1ec37221bf2a31ac0a20700') in 0.0490772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 200

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Resources/green_bullet.prefab
  artifactKey: Guid(95da0bbe51e1be04bb4c54c06edc5b63) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/green_bullet.prefab using Guid(95da0bbe51e1be04bb4c54c06edc5b63) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab945462f59a6976ca4450070141e9f4') in 0.039752 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_Slime.prefab
  artifactKey: Guid(4577809d5c725114b8c8d59aa17a99ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_Slime.prefab using Guid(4577809d5c725114b8c8d59aa17a99ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 26.51 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'ad7bfb6af27325185e772cc8c8d57c63') in 0.0962008 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 89

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Prefabs/Enemies/Boses/Boss_BigZombie.prefab
  artifactKey: Guid(a13849dbf73e2174d833444bca44e8d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Boses/Boss_BigZombie.prefab using Guid(a13849dbf73e2174d833444bca44e8d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 23.42 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '96f0c6013ffb555c8abbabc145ab5460') in 0.1029809 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Prefabs/Others/NPC/NPC_Aristocrate-F.prefab
  artifactKey: Guid(69ecf24a7a6e10d47bdd0b5ce762138f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/NPC/NPC_Aristocrate-F.prefab using Guid(69ecf24a7a6e10d47bdd0b5ce762138f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b546f90a5fb07f83f7b5485fc2f2ae50') in 0.0343153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/Prefabs/Others/NPC/NPC_Townfolk-F.prefab
  artifactKey: Guid(2966637e3290c484c90bba783d24607f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/NPC/NPC_Townfolk-F.prefab using Guid(2966637e3290c484c90bba783d24607f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c90fcbd9e444e147113598167a003660') in 0.070217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_Chort.prefab
  artifactKey: Guid(10a3239a67a7a144fbb411d383920465) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_Chort.prefab using Guid(10a3239a67a7a144fbb411d383920465) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 22.63 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '98ac1f01844de40ca1b7363d8a19fc8a') in 0.1034623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Prefabs/BlockedDoors/blocked_blue_side.prefab
  artifactKey: Guid(8bd0a08e4015e59418f615683f8c0185) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BlockedDoors/blocked_blue_side.prefab using Guid(8bd0a08e4015e59418f615683f8c0185) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c0ecd76a869761df966a24b157dcc2f8') in 0.025192 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Prefabs/Data/Datacontroller.prefab
  artifactKey: Guid(e33399e98ef5a4b4e9816053c5b1e52d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Data/Datacontroller.prefab using Guid(e33399e98ef5a4b4e9816053c5b1e52d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d1dab6d641b57828dc94a230d46807b') in 0.0051217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Resources/flame_bullet.prefab
  artifactKey: Guid(084960a87f9826b4b89a7a0fae1ca574) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/flame_bullet.prefab using Guid(084960a87f9826b4b89a7a0fae1ca574) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b95bc4e4bc1908b8a0e221b5d5913f5') in 0.0670669 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000124 seconds.
  path: Assets/Resources/red_bullet.prefab
  artifactKey: Guid(db8107a302f0c8040a022bd14c6f7b81) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/red_bullet.prefab using Guid(db8107a302f0c8040a022bd14c6f7b81) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f76f11da4d843327777c7b909f2bc19a') in 0.0300131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Prefabs/BlockedDoors/blocked_orange_side.prefab
  artifactKey: Guid(ea11c3e6dc8a44d4892787ffc30b153f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BlockedDoors/blocked_orange_side.prefab using Guid(ea11c3e6dc8a44d4892787ffc30b153f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '310a093586ccec57ba16550d68a958e8') in 0.025958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000110 seconds.
  path: Assets/Prefabs/Bullets/EmptyShells.prefab
  artifactKey: Guid(67f1aa6f91dfce541beb32cc24a79a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Bullets/EmptyShells.prefab using Guid(67f1aa6f91dfce541beb32cc24a79a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ae8c09fa3af0a5ff6cedff2cf281ed0') in 0.0277833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_Imp.prefab
  artifactKey: Guid(f27eab4b594788f4397bb416387f6433) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_Imp.prefab using Guid(f27eab4b594788f4397bb416387f6433) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 18.81 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '6903e5c7405f0784883cdc5967bbbfcf') in 0.0799206 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Prefabs/BlockedDoors/blocked_blue_front.prefab
  artifactKey: Guid(c28be40621c6353409ee50d8f76360ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BlockedDoors/blocked_blue_front.prefab using Guid(c28be40621c6353409ee50d8f76360ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58018258687a15210deea7c134389cd6') in 0.0225046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Prefabs/Others/NPC/NPC_Fighter-M.prefab
  artifactKey: Guid(8b2af43ead48fdd4f9c68d4f1a0bda24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/NPC/NPC_Fighter-M.prefab using Guid(8b2af43ead48fdd4f9c68d4f1a0bda24) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af08f9a012e10ec61d6e0ce9b94db7e0') in 0.066855 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Resources/canon_bullet.prefab
  artifactKey: Guid(677a6474f244ae949a850c186fb490fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/canon_bullet.prefab using Guid(677a6474f244ae949a850c186fb490fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da981e67994b34f9ea8120e55c8180d3') in 0.0318462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_MaskedOrc.prefab
  artifactKey: Guid(8107d2ce63c6d6744a4b71f1ac8026b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_MaskedOrc.prefab using Guid(8107d2ce63c6d6744a4b71f1ac8026b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 24.46 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'b0381419d6815b45de5d2d1a31242e3d') in 0.099316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Prefabs/Others/torch.prefab
  artifactKey: Guid(6f436ed7d82ce4b4d81da3103ce0289b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/torch.prefab using Guid(6f436ed7d82ce4b4d81da3103ce0289b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba61fbe757a720a77c20ce19abfe247c') in 0.0348955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Prefabs/UI/Canvas_Hud.prefab
  artifactKey: Guid(08573078048e167478107ea35eea1b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/UI/Canvas_Hud.prefab using Guid(08573078048e167478107ea35eea1b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a055be8483e959f9e1816af612b1b0ec') in 0.073264 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 347

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Prefabs/Enemies/Enemy_Simple_01.prefab
  artifactKey: Guid(07fa88da6c05a8e4193bbb34007a6cb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Enemy_Simple_01.prefab using Guid(07fa88da6c05a8e4193bbb34007a6cb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 30.75 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'bd371cf6b35cfafdb687e97395056070') in 0.0952221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 71

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Virtual Joystick Pack/Prefabs/Floating Joystick.prefab
  artifactKey: Guid(cdd5474b97f1a9d40bea678ca8dec2bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Virtual Joystick Pack/Prefabs/Floating Joystick.prefab using Guid(cdd5474b97f1a9d40bea678ca8dec2bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3675a2736e84315575dbd02ac7a03dc4') in 0.0083542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Artwork/Level/Tiles/Sewer/Sewer.prefab
  artifactKey: Guid(f623aeb7a603e7f4facfb470db5d4c0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Level/Tiles/Sewer/Sewer.prefab using Guid(f623aeb7a603e7f4facfb470db5d4c0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5bad76a59978308d4cba761113c90d79') in 0.6357532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 579

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Prefabs/Enemies/npc.prefab
  artifactKey: Guid(9cb823743a907614794427283685b2d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/npc.prefab using Guid(9cb823743a907614794427283685b2d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9d51a8b58e90d93da5132b0ce2adeb9c') in 0.023674 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Prefabs/Enemies/Boses/Boss_Ogre.prefab
  artifactKey: Guid(28fd4583c766680469d52b0222e571e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Boses/Boss_Ogre.prefab using Guid(28fd4583c766680469d52b0222e571e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 26.07 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'eb102441d28fe9a5ac9456db073d80ed') in 0.1032765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Prefabs/Weapons/Rocket.prefab
  artifactKey: Guid(c57b0a5291ef4bc45b3ae81ff2a17ffc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/Rocket.prefab using Guid(c57b0a5291ef4bc45b3ae81ff2a17ffc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '841891e99fc520fb1112563d1dfe3f53') in 0.010933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Resources/pink_bullet.prefab
  artifactKey: Guid(e8064fae858c5b14bb73078d72961be6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/pink_bullet.prefab using Guid(e8064fae858c5b14bb73078d72961be6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '418e370bcca02156c8c16245876ef83f') in 0.03881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Prefabs/BlockedDoors/blocked_yellow_side.prefab
  artifactKey: Guid(0fcf787ee4e1cd34f85db232d25f0cc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BlockedDoors/blocked_yellow_side.prefab using Guid(0fcf787ee4e1cd34f85db232d25f0cc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8aadf8574879a74110ea3e7bb65aa026') in 0.0297082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Prefabs/Enemies/Ranged/BossMinotaur.prefab
  artifactKey: Guid(df090c5c8f6c9234d88a85affce8b92a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Ranged/BossMinotaur.prefab using Guid(df090c5c8f6c9234d88a85affce8b92a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 29.36 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '44b92c239abc820da8f09ed7f94d251c') in 0.1335682 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 107

========================================================================
Received Import Request.
  Time since last request: 0.000120 seconds.
  path: Packages/com.unity.analytics/DataPrivacy/DataPrivacyButton.prefab
  artifactKey: Guid(71b11355001648444b41d17fd36c150d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.analytics/DataPrivacy/DataPrivacyButton.prefab using Guid(71b11355001648444b41d17fd36c150d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a842a5017caf4759d0e95f9abdd1eb7') in 0.018166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_NecroMancer.prefab
  artifactKey: Guid(7599f7c807f5a7a4e9dc021ac2e265f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_NecroMancer.prefab using Guid(7599f7c807f5a7a4e9dc021ac2e265f3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 20.48 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '1ee546020ebf8adbf398bceb83b1ed10') in 0.1007793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Resources/blue_bullet.prefab
  artifactKey: Guid(3005d63f9c64f7742a68267fbb6bee20) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/blue_bullet.prefab using Guid(3005d63f9c64f7742a68267fbb6bee20) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd6be702624e6b6dfa0715a9a22be0b3') in 0.0290595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Artwork/Level/Tiles/CharacterSelection/CharacterSelectionTown.prefab
  artifactKey: Guid(97f7c585d3fdc284a923bb2bfc86e467) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Level/Tiles/CharacterSelection/CharacterSelectionTown.prefab using Guid(97f7c585d3fdc284a923bb2bfc86e467) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4887234b4a7427f6e9a3e754f934a75e') in 0.7983413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 697

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Resources/yellow_bullet.prefab
  artifactKey: Guid(6cecd723512621247a30dbe8534d93b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/yellow_bullet.prefab using Guid(6cecd723512621247a30dbe8534d93b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b042e2f088042d194837e455f0fb8df') in 0.0367273 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Artwork/Level/Tiles/Town/Town.prefab
  artifactKey: Guid(cdcbabf3a7c76b242b722a3fd39853e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Artwork/Level/Tiles/Town/Town.prefab using Guid(cdcbabf3a7c76b242b722a3fd39853e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b3736c6914401ade399d7017f0cee20e') in 3.2376021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2835

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Prefabs/Others/Water_Fountain-Healing.prefab
  artifactKey: Guid(1e8d0989d43683944b03f596af1ce184) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Others/Water_Fountain-Healing.prefab using Guid(1e8d0989d43683944b03f596af1ce184) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '286953c4cc476c841b4fa76213d9e58c') in 0.043934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 36

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_OrcShaman.prefab
  artifactKey: Guid(59b7d28f8de70cf4eb7b53f8e58ddd84) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_OrcShaman.prefab using Guid(59b7d28f8de70cf4eb7b53f8e58ddd84) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 23.91 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'fe804f1777d3d3a985315b47f86bca78') in 0.1003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_Zombie.prefab
  artifactKey: Guid(feeeb155d136fa744a0b4ccb5c17bff2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_Zombie.prefab using Guid(feeeb155d136fa744a0b4ccb5c17bff2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 26.79 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'ac95b33ad5c2164212d8417a8d0dbfc7') in 0.0926905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Prefabs/VideoPlayer.prefab
  artifactKey: Guid(3ebe3e513a05a5548b64b0863a38ef97) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/VideoPlayer.prefab using Guid(3ebe3e513a05a5548b64b0863a38ef97) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5beb24f7374aa50e181813afa60193c9') in 0.0296101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Prefabs/Weapons/FlameThrower.prefab
  artifactKey: Guid(89f29005a077b2d46a17dedf35f772b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/FlameThrower.prefab using Guid(89f29005a077b2d46a17dedf35f772b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f489da8daa047a9aeb94d7182e43b65f') in 0.0106943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Resources/matter_bullet.prefab
  artifactKey: Guid(1f540ec2a5ac94f4c8c3eed0781b67ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/matter_bullet.prefab using Guid(1f540ec2a5ac94f4c8c3eed0781b67ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83391ded363c0545d07a1174aa6b2a9e') in 0.0272307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Prefabs/BlockedDoors/DisableEnemyTarget.prefab
  artifactKey: Guid(b3b1f7a4da5b79b43bd56ef08ccb19c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/BlockedDoors/DisableEnemyTarget.prefab using Guid(b3b1f7a4da5b79b43bd56ef08ccb19c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9093d0e4cd1877d3fda664003fb0b364') in 0.0039146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000112 seconds.
  path: Assets/Prefabs/Weapons/Shotgun.prefab
  artifactKey: Guid(26266a20f7e397e4ab218a455a807638) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Weapons/Shotgun.prefab using Guid(26266a20f7e397e4ab218a455a807638) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd93a4e58394b30d4582d457385b8860e') in 0.0085713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Prefabs/Enemies/Ranged/Devil.prefab
  artifactKey: Guid(38ed6cbaaa0a8f54a9144bf1e0c92728) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Ranged/Devil.prefab using Guid(38ed6cbaaa0a8f54a9144bf1e0c92728) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 30.16 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'd3b0d916d12b72b322d2f54dfc882ea5') in 0.0932232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Prefabs/Enemies/Melee/Enemy_Wogol.prefab
  artifactKey: Guid(74508cabf528750468e53999385da098) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Enemies/Melee/Enemy_Wogol.prefab using Guid(74508cabf528750468e53999385da098) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 33.51 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '9dfadab60aff795931622e98edf1c0bf') in 0.1043738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 91

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Virtual Joystick Pack/Prefabs/Fixed Joystick.prefab
  artifactKey: Guid(ce5089f3caa5bb042a4d4c23a427bc82) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Virtual Joystick Pack/Prefabs/Fixed Joystick.prefab using Guid(ce5089f3caa5bb042a4d4c23a427bc82) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b0a8f449d1261f7d32905fa07886ae0') in 0.0087741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Packages/com.unity.xr.legacyinputhelpers/Prefabs/XRRig.prefab
  artifactKey: Guid(c116b7497ab8ca34c9188965d4295e27) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.xr.legacyinputhelpers/Prefabs/XRRig.prefab using Guid(c116b7497ab8ca34c9188965d4295e27) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e27d514a7323e5c253f0166a97d6d27c') in 0.0149826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

