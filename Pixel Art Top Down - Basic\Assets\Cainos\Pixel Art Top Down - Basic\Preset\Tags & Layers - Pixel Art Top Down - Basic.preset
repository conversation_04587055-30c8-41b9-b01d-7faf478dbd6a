%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Tags & Layers - Pixel Art Top Down - Basic
  m_TargetType:
    m_NativeTypeID: 78
    m_ManagedTypePPtr: {fileID: 0}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: tags.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.size
    value: 32
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[0]
    value: Default
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[1]
    value: TransparentFX
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[2]
    value: Ignore Raycast
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[3]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[4]
    value: Water
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[5]
    value: UI
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[6]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[7]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[8]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[9]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[10]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[11]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[12]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[13]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[14]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[15]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[16]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[17]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[18]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[19]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[20]
    value: Layer 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[21]
    value: Layer 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[22]
    value: Layer 3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[23]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[24]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[25]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[26]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[27]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[28]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[29]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[30]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: layers.Array.data[31]
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.size
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[0].name
    value: Default
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[0].uniqueID
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[0].locked
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[1].name
    value: Layer 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[1].uniqueID
    value: 2425651459
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[1].locked
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[2].name
    value: Layer 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[2].uniqueID
    value: 4250941897
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[2].locked
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[3].name
    value: Layer 3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[3].uniqueID
    value: 4189426099
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SortingLayers.Array.data[3].locked
    value: 0
    objectReference: {fileID: 0}
